package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_preferences")
data class UserPreferences(
    @PrimaryKey
    val id: Long = 1, // Single row for user preferences
    val budgetPeriod: String = "weekly", // "weekly" or "monthly"
    val notificationsEnabled: Boolean = true,
    val reminderTime: String = "09:00", // HH:mm format
    val darkModeEnabled: Boolean = false,
    val fontSize: String = "medium", // "small", "medium", "large"
    val primaryGoal: String? = null, // "debt_payoff", "savings", "budgeting"
    val weeklyBudget: Double? = null,
    val monthlyBudget: Double? = null,
    val hasCompletedOnboarding: Boolean = false,
    val enableNotifications: Boolean = true,
    val notificationTime: String = "09:00",
    val theme: String = "system", // "light", "dark", "system"
    // Zero-based budgeting enhancements
    val weeklyIncome: Double? = null,
    val monthlyIncome: Double? = null,
    val useZeroBasedBudgeting: Boolean = false,
    val envelopeViewEnabled: Boolean = true,
    val themePreference: String = "SYSTEM", // "SYSTEM", "LIGHT", "DARK", "HIGH_CONTRAST_LIGHT", "HIGH_CONTRAST_DARK"
    val fontScale: Float = 1.0f, // Font scaling for accessibility
    val highContrastMode: Boolean = false,
    val voiceInputEnabled: Boolean = true,
    val animationsEnabled: Boolean = true
)

