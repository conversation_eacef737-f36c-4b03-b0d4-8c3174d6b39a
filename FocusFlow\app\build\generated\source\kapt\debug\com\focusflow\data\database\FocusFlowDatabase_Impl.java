package com.focusflow.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.focusflow.data.dao.AIInteractionDao;
import com.focusflow.data.dao.AIInteractionDao_Impl;
import com.focusflow.data.dao.AccountabilityContactDao;
import com.focusflow.data.dao.AccountabilityContactDao_Impl;
import com.focusflow.data.dao.AchievementDao;
import com.focusflow.data.dao.AchievementDao_Impl;
import com.focusflow.data.dao.AlternativeProductDao;
import com.focusflow.data.dao.AlternativeProductDao_Impl;
import com.focusflow.data.dao.BudgetAnalyticsDao;
import com.focusflow.data.dao.BudgetAnalyticsDao_Impl;
import com.focusflow.data.dao.BudgetCategoryDao;
import com.focusflow.data.dao.BudgetCategoryDao_Impl;
import com.focusflow.data.dao.BudgetRecommendationDao;
import com.focusflow.data.dao.BudgetRecommendationDao_Impl;
import com.focusflow.data.dao.CreditCardDao;
import com.focusflow.data.dao.CreditCardDao_Impl;
import com.focusflow.data.dao.DashboardWidgetDao;
import com.focusflow.data.dao.DashboardWidgetDao_Impl;
import com.focusflow.data.dao.ExpenseDao;
import com.focusflow.data.dao.ExpenseDao_Impl;
import com.focusflow.data.dao.FocusSessionDao;
import com.focusflow.data.dao.FocusSessionDao_Impl;
import com.focusflow.data.dao.HabitLogDao;
import com.focusflow.data.dao.HabitLogDao_Impl;
import com.focusflow.data.dao.SpendingPatternDao;
import com.focusflow.data.dao.SpendingPatternDao_Impl;
import com.focusflow.data.dao.SpendingReflectionDao;
import com.focusflow.data.dao.SpendingReflectionDao_Impl;
import com.focusflow.data.dao.TaskDao;
import com.focusflow.data.dao.TaskDao_Impl;
import com.focusflow.data.dao.UserPreferencesDao;
import com.focusflow.data.dao.UserPreferencesDao_Impl;
import com.focusflow.data.dao.UserStatsDao;
import com.focusflow.data.dao.UserStatsDao_Impl;
import com.focusflow.data.dao.VirtualPetDao;
import com.focusflow.data.dao.VirtualPetDao_Impl;
import com.focusflow.data.dao.VoiceCommandDao;
import com.focusflow.data.dao.VoiceCommandDao_Impl;
import com.focusflow.data.dao.WishlistItemDao;
import com.focusflow.data.dao.WishlistItemDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class FocusFlowDatabase_Impl extends FocusFlowDatabase {
  private volatile ExpenseDao _expenseDao;

  private volatile CreditCardDao _creditCardDao;

  private volatile BudgetCategoryDao _budgetCategoryDao;

  private volatile HabitLogDao _habitLogDao;

  private volatile TaskDao _taskDao;

  private volatile AIInteractionDao _aIInteractionDao;

  private volatile UserPreferencesDao _userPreferencesDao;

  private volatile AchievementDao _achievementDao;

  private volatile UserStatsDao _userStatsDao;

  private volatile VirtualPetDao _virtualPetDao;

  private volatile WishlistItemDao _wishlistItemDao;

  private volatile BudgetRecommendationDao _budgetRecommendationDao;

  private volatile SpendingReflectionDao _spendingReflectionDao;

  private volatile BudgetAnalyticsDao _budgetAnalyticsDao;

  private volatile FocusSessionDao _focusSessionDao;

  private volatile SpendingPatternDao _spendingPatternDao;

  private volatile AlternativeProductDao _alternativeProductDao;

  private volatile AccountabilityContactDao _accountabilityContactDao;

  private volatile DashboardWidgetDao _dashboardWidgetDao;

  private volatile VoiceCommandDao _voiceCommandDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(6) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `expenses` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `amount` REAL NOT NULL, `category` TEXT NOT NULL, `description` TEXT NOT NULL, `merchant` TEXT, `date` TEXT NOT NULL, `receiptPath` TEXT, `isRecurring` INTEGER NOT NULL, `recurringFrequency` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `credit_cards` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `currentBalance` REAL NOT NULL, `creditLimit` REAL NOT NULL, `minimumPayment` REAL NOT NULL, `dueDate` TEXT NOT NULL, `interestRate` REAL NOT NULL, `lastPaymentAmount` REAL, `lastPaymentDate` TEXT, `isActive` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `budget_categories` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `allocatedAmount` REAL NOT NULL, `spentAmount` REAL NOT NULL, `budgetPeriod` TEXT NOT NULL, `budgetYear` INTEGER NOT NULL, `budgetMonth` INTEGER, `budgetWeek` INTEGER, `isActive` INTEGER NOT NULL, `recommendedAmount` REAL, `lastRecommendationUpdate` TEXT, `varianceThreshold` REAL NOT NULL, `categoryColor` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `habit_logs` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `habitType` TEXT NOT NULL, `date` TEXT NOT NULL, `value` TEXT NOT NULL, `notes` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `tasks` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `title` TEXT NOT NULL, `description` TEXT, `isCompleted` INTEGER NOT NULL, `dueDate` TEXT, `priority` TEXT NOT NULL, `category` TEXT, `isRecurring` INTEGER NOT NULL, `recurringFrequency` TEXT, `createdAt` TEXT NOT NULL, `completedAt` TEXT, `estimatedDuration` INTEGER)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `ai_interactions` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userMessage` TEXT NOT NULL, `aiResponse` TEXT NOT NULL, `interactionType` TEXT NOT NULL, `timestamp` TEXT NOT NULL, `contextData` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_preferences` (`id` INTEGER NOT NULL, `budgetPeriod` TEXT NOT NULL, `notificationsEnabled` INTEGER NOT NULL, `reminderTime` TEXT NOT NULL, `darkModeEnabled` INTEGER NOT NULL, `fontSize` TEXT NOT NULL, `primaryGoal` TEXT, `weeklyBudget` REAL, `monthlyBudget` REAL, `hasCompletedOnboarding` INTEGER NOT NULL, `enableNotifications` INTEGER NOT NULL, `notificationTime` TEXT NOT NULL, `theme` TEXT NOT NULL, `themePreference` TEXT NOT NULL, `fontScale` REAL NOT NULL, `highContrastMode` INTEGER NOT NULL, `voiceInputEnabled` INTEGER NOT NULL, `animationsEnabled` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `achievements` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `iconEmoji` TEXT NOT NULL, `pointsAwarded` INTEGER NOT NULL, `isUnlocked` INTEGER NOT NULL, `unlockedAt` TEXT, `targetValue` INTEGER, `currentProgress` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_stats` (`id` INTEGER NOT NULL, `totalPoints` INTEGER NOT NULL, `currentLevel` INTEGER NOT NULL, `expenseLoggingStreak` INTEGER NOT NULL, `budgetAdherenceStreak` INTEGER NOT NULL, `totalExpensesLogged` INTEGER NOT NULL, `totalDebtPaid` REAL NOT NULL, `achievementsUnlocked` INTEGER NOT NULL, `lastActivityDate` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `virtual_pet` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `type` TEXT NOT NULL, `level` INTEGER NOT NULL, `happiness` INTEGER NOT NULL, `health` INTEGER NOT NULL, `experience` INTEGER NOT NULL, `lastFed` TEXT, `lastPlayed` TEXT, `accessories` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `wishlist_items` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `itemName` TEXT NOT NULL, `estimatedPrice` REAL NOT NULL, `category` TEXT NOT NULL, `description` TEXT, `merchant` TEXT, `addedDate` TEXT NOT NULL, `delayPeriodHours` INTEGER NOT NULL, `isDelayActive` INTEGER NOT NULL, `delayEndTime` TEXT NOT NULL, `priority` TEXT NOT NULL, `tags` TEXT, `imageUrl` TEXT, `productUrl` TEXT, `isPurchased` INTEGER NOT NULL, `purchasedDate` TEXT, `actualPrice` REAL, `reflectionNotes` TEXT, `stillWanted` INTEGER)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `budget_recommendations` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `categoryName` TEXT NOT NULL, `recommendedAmount` REAL NOT NULL, `currentAmount` REAL NOT NULL, `confidenceScore` REAL NOT NULL, `reasonCode` TEXT NOT NULL, `reasonDescription` TEXT NOT NULL, `generatedDate` TEXT NOT NULL, `isAccepted` INTEGER, `userFeedback` TEXT, `basedOnDays` INTEGER NOT NULL, `seasonalFactor` REAL NOT NULL, `trendFactor` REAL NOT NULL, `varianceFactor` REAL NOT NULL, `isActive` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `spending_reflections` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `expenseId` INTEGER, `wishlistItemId` INTEGER, `amount` REAL NOT NULL, `category` TEXT NOT NULL, `itemDescription` TEXT NOT NULL, `reflectionDate` TEXT NOT NULL, `emotionalState` TEXT, `triggerReason` TEXT, `needVsWant` TEXT, `satisfactionLevel` INTEGER, `regretLevel` INTEGER, `wouldBuyAgain` INTEGER, `alternativeConsidered` TEXT, `delayHelpful` INTEGER, `notes` TEXT, `mindfulnessScore` INTEGER, `budgetImpact` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `budget_analytics` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `categoryName` TEXT NOT NULL, `budgetPeriod` TEXT NOT NULL, `budgetYear` INTEGER NOT NULL, `budgetMonth` INTEGER, `budgetWeek` INTEGER, `plannedAmount` REAL NOT NULL, `actualSpent` REAL NOT NULL, `variance` REAL NOT NULL, `variancePercentage` REAL NOT NULL, `trendDirection` TEXT NOT NULL, `averageTransactionSize` REAL NOT NULL, `transactionCount` INTEGER NOT NULL, `largestTransaction` REAL NOT NULL, `smallestTransaction` REAL NOT NULL, `mostFrequentMerchant` TEXT, `calculatedDate` TEXT NOT NULL, `daysInPeriod` INTEGER NOT NULL, `projectedEndAmount` REAL, `recommendedAdjustment` REAL, `seasonalityFactor` REAL NOT NULL, `isOutlierPeriod` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `focus_sessions` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `taskType` TEXT NOT NULL, `sessionName` TEXT NOT NULL, `plannedDurationMinutes` INTEGER NOT NULL, `actualDurationMinutes` INTEGER, `startTime` TEXT NOT NULL, `endTime` TEXT, `isCompleted` INTEGER NOT NULL, `wasInterrupted` INTEGER NOT NULL, `interruptionCount` INTEGER NOT NULL, `breaksTaken` INTEGER NOT NULL, `focusQuality` INTEGER, `productivityScore` INTEGER, `notes` TEXT, `tasksCompleted` TEXT, `distractions` TEXT, `sessionGoal` TEXT, `goalAchieved` INTEGER, `energyLevelBefore` INTEGER, `energyLevelAfter` INTEGER, `moodBefore` TEXT, `moodAfter` TEXT, `pomodoroCount` INTEGER NOT NULL, `sessionType` TEXT NOT NULL, `backgroundSound` TEXT, `isSuccessful` INTEGER)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `spending_patterns` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `patternType` TEXT NOT NULL, `patternName` TEXT NOT NULL, `description` TEXT NOT NULL, `triggerConditions` TEXT NOT NULL, `averageAmount` REAL NOT NULL, `frequency` TEXT NOT NULL, `category` TEXT NOT NULL, `emotionalTriggers` TEXT, `timePatterns` TEXT, `locationTriggers` TEXT, `socialTriggers` TEXT, `detectedDate` TEXT NOT NULL, `lastOccurrence` TEXT, `occurrenceCount` INTEGER NOT NULL, `confidenceScore` REAL NOT NULL, `isActive` INTEGER NOT NULL, `userConfirmed` INTEGER, `interventionStrategy` TEXT, `interventionEffectiveness` REAL, `notes` TEXT, `severity` TEXT NOT NULL, `impactOnBudget` TEXT NOT NULL, `relatedPatterns` TEXT, `seasonalFactor` REAL NOT NULL, `stressLevel` INTEGER, `preventionSuccess` INTEGER NOT NULL, `preventionAttempts` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `alternative_products` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `originalProductName` TEXT NOT NULL, `originalPrice` REAL NOT NULL, `originalCategory` TEXT NOT NULL, `alternativeName` TEXT NOT NULL, `alternativePrice` REAL NOT NULL, `alternativeCategory` TEXT, `alternativeType` TEXT NOT NULL, `savingsAmount` REAL NOT NULL, `savingsPercentage` REAL NOT NULL, `description` TEXT NOT NULL, `pros` TEXT, `cons` TEXT, `availabilityInfo` TEXT, `qualityRating` INTEGER, `userRating` INTEGER, `suggestionSource` TEXT NOT NULL, `confidenceScore` REAL NOT NULL, `createdDate` TEXT NOT NULL, `lastSuggested` TEXT, `timesShown` INTEGER NOT NULL, `timesAccepted` INTEGER NOT NULL, `timesRejected` INTEGER NOT NULL, `userFeedback` TEXT, `isActive` INTEGER NOT NULL, `tags` TEXT, `imageUrl` TEXT, `productUrl` TEXT, `merchant` TEXT, `estimatedDeliveryTime` TEXT, `sustainabilityScore` INTEGER, `difficultyLevel` TEXT NOT NULL, `timeInvestment` TEXT, `requiredSkills` TEXT, `successRate` REAL, `relatedAlternatives` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `accountability_contacts` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `relationship` TEXT NOT NULL, `contactMethod` TEXT NOT NULL, `contactInfo` TEXT NOT NULL, `isActive` INTEGER NOT NULL, `permissionLevel` TEXT NOT NULL, `sharingPreferences` TEXT NOT NULL, `addedDate` TEXT NOT NULL, `lastContactDate` TEXT, `totalInteractions` INTEGER NOT NULL, `successfulInterventions` INTEGER NOT NULL, `trustLevel` INTEGER NOT NULL, `responseRate` REAL NOT NULL, `averageResponseTime` INTEGER, `preferredContactTime` TEXT, `timeZone` TEXT, `notificationFrequency` TEXT NOT NULL, `supportType` TEXT NOT NULL, `specializations` TEXT, `notes` TEXT, `emergencyContact` INTEGER NOT NULL, `canReceiveSpendingAlerts` INTEGER NOT NULL, `canReceiveBudgetUpdates` INTEGER NOT NULL, `canReceiveGoalProgress` INTEGER NOT NULL, `canReceiveEmergencyAlerts` INTEGER NOT NULL, `mutualAccountability` INTEGER NOT NULL, `partnerUserId` TEXT, `consentGiven` INTEGER NOT NULL, `consentDate` TEXT, `lastConsentUpdate` TEXT, `privacyLevel` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `dashboard_widgets` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `widgetType` TEXT NOT NULL, `displayName` TEXT NOT NULL, `position` INTEGER NOT NULL, `isVisible` INTEGER NOT NULL, `isEnabled` INTEGER NOT NULL, `size` TEXT NOT NULL, `configuration` TEXT, `refreshInterval` INTEGER NOT NULL, `lastUpdated` TEXT, `dataSource` TEXT, `customTitle` TEXT, `colorScheme` TEXT NOT NULL, `customColors` TEXT, `showHeader` INTEGER NOT NULL, `showFooter` INTEGER NOT NULL, `headerText` TEXT, `footerText` TEXT, `iconName` TEXT, `animationEnabled` INTEGER NOT NULL, `clickAction` TEXT, `longPressAction` TEXT, `swipeActions` TEXT, `accessibilityLabel` TEXT, `accessibilityHint` TEXT, `isCustomizable` INTEGER NOT NULL, `requiresPermission` INTEGER NOT NULL, `permissionType` TEXT, `dataRetentionDays` INTEGER NOT NULL, `cacheEnabled` INTEGER NOT NULL, `offlineSupport` INTEGER NOT NULL, `errorFallback` TEXT, `loadingIndicator` TEXT NOT NULL, `updateAnimation` TEXT NOT NULL, `priority` INTEGER NOT NULL, `dependencies` TEXT, `createdDate` TEXT NOT NULL, `modifiedDate` TEXT, `userNotes` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `voice_commands` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `commandText` TEXT NOT NULL, `recognizedText` TEXT NOT NULL, `commandType` TEXT NOT NULL, `intent` TEXT NOT NULL, `parameters` TEXT, `isSuccessful` INTEGER NOT NULL, `confidence` REAL NOT NULL, `processingTime` INTEGER NOT NULL, `timestamp` TEXT NOT NULL, `userId` TEXT, `sessionId` TEXT, `context` TEXT, `followUpRequired` INTEGER NOT NULL, `followUpPrompt` TEXT, `errorMessage` TEXT, `correctedCommand` TEXT, `actionTaken` TEXT, `resultData` TEXT, `userFeedback` TEXT, `language` TEXT NOT NULL, `accent` TEXT, `noiseLevel` TEXT, `deviceType` TEXT, `isOffline` INTEGER NOT NULL, `retryCount` INTEGER NOT NULL, `alternativeCommands` TEXT, `learningData` TEXT, `privacyLevel` TEXT NOT NULL, `isTrainingData` INTEGER NOT NULL, `customVocabulary` TEXT, `shortcuts` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '688b39ab097885dd242d4141f16d16aa')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `expenses`");
        db.execSQL("DROP TABLE IF EXISTS `credit_cards`");
        db.execSQL("DROP TABLE IF EXISTS `budget_categories`");
        db.execSQL("DROP TABLE IF EXISTS `habit_logs`");
        db.execSQL("DROP TABLE IF EXISTS `tasks`");
        db.execSQL("DROP TABLE IF EXISTS `ai_interactions`");
        db.execSQL("DROP TABLE IF EXISTS `user_preferences`");
        db.execSQL("DROP TABLE IF EXISTS `achievements`");
        db.execSQL("DROP TABLE IF EXISTS `user_stats`");
        db.execSQL("DROP TABLE IF EXISTS `virtual_pet`");
        db.execSQL("DROP TABLE IF EXISTS `wishlist_items`");
        db.execSQL("DROP TABLE IF EXISTS `budget_recommendations`");
        db.execSQL("DROP TABLE IF EXISTS `spending_reflections`");
        db.execSQL("DROP TABLE IF EXISTS `budget_analytics`");
        db.execSQL("DROP TABLE IF EXISTS `focus_sessions`");
        db.execSQL("DROP TABLE IF EXISTS `spending_patterns`");
        db.execSQL("DROP TABLE IF EXISTS `alternative_products`");
        db.execSQL("DROP TABLE IF EXISTS `accountability_contacts`");
        db.execSQL("DROP TABLE IF EXISTS `dashboard_widgets`");
        db.execSQL("DROP TABLE IF EXISTS `voice_commands`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsExpenses = new HashMap<String, TableInfo.Column>(9);
        _columnsExpenses.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("amount", new TableInfo.Column("amount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("merchant", new TableInfo.Column("merchant", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("receiptPath", new TableInfo.Column("receiptPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("isRecurring", new TableInfo.Column("isRecurring", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsExpenses.put("recurringFrequency", new TableInfo.Column("recurringFrequency", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysExpenses = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesExpenses = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoExpenses = new TableInfo("expenses", _columnsExpenses, _foreignKeysExpenses, _indicesExpenses);
        final TableInfo _existingExpenses = TableInfo.read(db, "expenses");
        if (!_infoExpenses.equals(_existingExpenses)) {
          return new RoomOpenHelper.ValidationResult(false, "expenses(com.focusflow.data.model.Expense).\n"
                  + " Expected:\n" + _infoExpenses + "\n"
                  + " Found:\n" + _existingExpenses);
        }
        final HashMap<String, TableInfo.Column> _columnsCreditCards = new HashMap<String, TableInfo.Column>(10);
        _columnsCreditCards.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("currentBalance", new TableInfo.Column("currentBalance", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("creditLimit", new TableInfo.Column("creditLimit", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("minimumPayment", new TableInfo.Column("minimumPayment", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("dueDate", new TableInfo.Column("dueDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("interestRate", new TableInfo.Column("interestRate", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("lastPaymentAmount", new TableInfo.Column("lastPaymentAmount", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("lastPaymentDate", new TableInfo.Column("lastPaymentDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCreditCards.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCreditCards = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCreditCards = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCreditCards = new TableInfo("credit_cards", _columnsCreditCards, _foreignKeysCreditCards, _indicesCreditCards);
        final TableInfo _existingCreditCards = TableInfo.read(db, "credit_cards");
        if (!_infoCreditCards.equals(_existingCreditCards)) {
          return new RoomOpenHelper.ValidationResult(false, "credit_cards(com.focusflow.data.model.CreditCard).\n"
                  + " Expected:\n" + _infoCreditCards + "\n"
                  + " Found:\n" + _existingCreditCards);
        }
        final HashMap<String, TableInfo.Column> _columnsBudgetCategories = new HashMap<String, TableInfo.Column>(13);
        _columnsBudgetCategories.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("allocatedAmount", new TableInfo.Column("allocatedAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("spentAmount", new TableInfo.Column("spentAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("budgetPeriod", new TableInfo.Column("budgetPeriod", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("budgetYear", new TableInfo.Column("budgetYear", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("budgetMonth", new TableInfo.Column("budgetMonth", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("budgetWeek", new TableInfo.Column("budgetWeek", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("recommendedAmount", new TableInfo.Column("recommendedAmount", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("lastRecommendationUpdate", new TableInfo.Column("lastRecommendationUpdate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("varianceThreshold", new TableInfo.Column("varianceThreshold", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetCategories.put("categoryColor", new TableInfo.Column("categoryColor", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysBudgetCategories = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesBudgetCategories = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoBudgetCategories = new TableInfo("budget_categories", _columnsBudgetCategories, _foreignKeysBudgetCategories, _indicesBudgetCategories);
        final TableInfo _existingBudgetCategories = TableInfo.read(db, "budget_categories");
        if (!_infoBudgetCategories.equals(_existingBudgetCategories)) {
          return new RoomOpenHelper.ValidationResult(false, "budget_categories(com.focusflow.data.model.BudgetCategory).\n"
                  + " Expected:\n" + _infoBudgetCategories + "\n"
                  + " Found:\n" + _existingBudgetCategories);
        }
        final HashMap<String, TableInfo.Column> _columnsHabitLogs = new HashMap<String, TableInfo.Column>(5);
        _columnsHabitLogs.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitLogs.put("habitType", new TableInfo.Column("habitType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitLogs.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitLogs.put("value", new TableInfo.Column("value", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitLogs.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysHabitLogs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesHabitLogs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoHabitLogs = new TableInfo("habit_logs", _columnsHabitLogs, _foreignKeysHabitLogs, _indicesHabitLogs);
        final TableInfo _existingHabitLogs = TableInfo.read(db, "habit_logs");
        if (!_infoHabitLogs.equals(_existingHabitLogs)) {
          return new RoomOpenHelper.ValidationResult(false, "habit_logs(com.focusflow.data.model.HabitLog).\n"
                  + " Expected:\n" + _infoHabitLogs + "\n"
                  + " Found:\n" + _existingHabitLogs);
        }
        final HashMap<String, TableInfo.Column> _columnsTasks = new HashMap<String, TableInfo.Column>(12);
        _columnsTasks.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("dueDate", new TableInfo.Column("dueDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("priority", new TableInfo.Column("priority", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("category", new TableInfo.Column("category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("isRecurring", new TableInfo.Column("isRecurring", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("recurringFrequency", new TableInfo.Column("recurringFrequency", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("createdAt", new TableInfo.Column("createdAt", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("completedAt", new TableInfo.Column("completedAt", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("estimatedDuration", new TableInfo.Column("estimatedDuration", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTasks = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTasks = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTasks = new TableInfo("tasks", _columnsTasks, _foreignKeysTasks, _indicesTasks);
        final TableInfo _existingTasks = TableInfo.read(db, "tasks");
        if (!_infoTasks.equals(_existingTasks)) {
          return new RoomOpenHelper.ValidationResult(false, "tasks(com.focusflow.data.model.Task).\n"
                  + " Expected:\n" + _infoTasks + "\n"
                  + " Found:\n" + _existingTasks);
        }
        final HashMap<String, TableInfo.Column> _columnsAiInteractions = new HashMap<String, TableInfo.Column>(6);
        _columnsAiInteractions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAiInteractions.put("userMessage", new TableInfo.Column("userMessage", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAiInteractions.put("aiResponse", new TableInfo.Column("aiResponse", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAiInteractions.put("interactionType", new TableInfo.Column("interactionType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAiInteractions.put("timestamp", new TableInfo.Column("timestamp", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAiInteractions.put("contextData", new TableInfo.Column("contextData", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAiInteractions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAiInteractions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAiInteractions = new TableInfo("ai_interactions", _columnsAiInteractions, _foreignKeysAiInteractions, _indicesAiInteractions);
        final TableInfo _existingAiInteractions = TableInfo.read(db, "ai_interactions");
        if (!_infoAiInteractions.equals(_existingAiInteractions)) {
          return new RoomOpenHelper.ValidationResult(false, "ai_interactions(com.focusflow.data.model.AIInteraction).\n"
                  + " Expected:\n" + _infoAiInteractions + "\n"
                  + " Found:\n" + _existingAiInteractions);
        }
        final HashMap<String, TableInfo.Column> _columnsUserPreferences = new HashMap<String, TableInfo.Column>(18);
        _columnsUserPreferences.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("budgetPeriod", new TableInfo.Column("budgetPeriod", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("notificationsEnabled", new TableInfo.Column("notificationsEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("reminderTime", new TableInfo.Column("reminderTime", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("darkModeEnabled", new TableInfo.Column("darkModeEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("fontSize", new TableInfo.Column("fontSize", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("primaryGoal", new TableInfo.Column("primaryGoal", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("weeklyBudget", new TableInfo.Column("weeklyBudget", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("monthlyBudget", new TableInfo.Column("monthlyBudget", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("hasCompletedOnboarding", new TableInfo.Column("hasCompletedOnboarding", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("enableNotifications", new TableInfo.Column("enableNotifications", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("notificationTime", new TableInfo.Column("notificationTime", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("theme", new TableInfo.Column("theme", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("themePreference", new TableInfo.Column("themePreference", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("fontScale", new TableInfo.Column("fontScale", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("highContrastMode", new TableInfo.Column("highContrastMode", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("voiceInputEnabled", new TableInfo.Column("voiceInputEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserPreferences.put("animationsEnabled", new TableInfo.Column("animationsEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserPreferences = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserPreferences = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserPreferences = new TableInfo("user_preferences", _columnsUserPreferences, _foreignKeysUserPreferences, _indicesUserPreferences);
        final TableInfo _existingUserPreferences = TableInfo.read(db, "user_preferences");
        if (!_infoUserPreferences.equals(_existingUserPreferences)) {
          return new RoomOpenHelper.ValidationResult(false, "user_preferences(com.focusflow.data.model.UserPreferences).\n"
                  + " Expected:\n" + _infoUserPreferences + "\n"
                  + " Found:\n" + _existingUserPreferences);
        }
        final HashMap<String, TableInfo.Column> _columnsAchievements = new HashMap<String, TableInfo.Column>(10);
        _columnsAchievements.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("iconEmoji", new TableInfo.Column("iconEmoji", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("pointsAwarded", new TableInfo.Column("pointsAwarded", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("isUnlocked", new TableInfo.Column("isUnlocked", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("unlockedAt", new TableInfo.Column("unlockedAt", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("targetValue", new TableInfo.Column("targetValue", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievements.put("currentProgress", new TableInfo.Column("currentProgress", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAchievements = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAchievements = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAchievements = new TableInfo("achievements", _columnsAchievements, _foreignKeysAchievements, _indicesAchievements);
        final TableInfo _existingAchievements = TableInfo.read(db, "achievements");
        if (!_infoAchievements.equals(_existingAchievements)) {
          return new RoomOpenHelper.ValidationResult(false, "achievements(com.focusflow.data.model.Achievement).\n"
                  + " Expected:\n" + _infoAchievements + "\n"
                  + " Found:\n" + _existingAchievements);
        }
        final HashMap<String, TableInfo.Column> _columnsUserStats = new HashMap<String, TableInfo.Column>(9);
        _columnsUserStats.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("totalPoints", new TableInfo.Column("totalPoints", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("currentLevel", new TableInfo.Column("currentLevel", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("expenseLoggingStreak", new TableInfo.Column("expenseLoggingStreak", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("budgetAdherenceStreak", new TableInfo.Column("budgetAdherenceStreak", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("totalExpensesLogged", new TableInfo.Column("totalExpensesLogged", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("totalDebtPaid", new TableInfo.Column("totalDebtPaid", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("achievementsUnlocked", new TableInfo.Column("achievementsUnlocked", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserStats.put("lastActivityDate", new TableInfo.Column("lastActivityDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserStats = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserStats = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserStats = new TableInfo("user_stats", _columnsUserStats, _foreignKeysUserStats, _indicesUserStats);
        final TableInfo _existingUserStats = TableInfo.read(db, "user_stats");
        if (!_infoUserStats.equals(_existingUserStats)) {
          return new RoomOpenHelper.ValidationResult(false, "user_stats(com.focusflow.data.model.UserStats).\n"
                  + " Expected:\n" + _infoUserStats + "\n"
                  + " Found:\n" + _existingUserStats);
        }
        final HashMap<String, TableInfo.Column> _columnsVirtualPet = new HashMap<String, TableInfo.Column>(10);
        _columnsVirtualPet.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("level", new TableInfo.Column("level", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("happiness", new TableInfo.Column("happiness", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("health", new TableInfo.Column("health", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("experience", new TableInfo.Column("experience", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("lastFed", new TableInfo.Column("lastFed", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("lastPlayed", new TableInfo.Column("lastPlayed", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVirtualPet.put("accessories", new TableInfo.Column("accessories", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysVirtualPet = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesVirtualPet = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoVirtualPet = new TableInfo("virtual_pet", _columnsVirtualPet, _foreignKeysVirtualPet, _indicesVirtualPet);
        final TableInfo _existingVirtualPet = TableInfo.read(db, "virtual_pet");
        if (!_infoVirtualPet.equals(_existingVirtualPet)) {
          return new RoomOpenHelper.ValidationResult(false, "virtual_pet(com.focusflow.data.model.VirtualPet).\n"
                  + " Expected:\n" + _infoVirtualPet + "\n"
                  + " Found:\n" + _existingVirtualPet);
        }
        final HashMap<String, TableInfo.Column> _columnsWishlistItems = new HashMap<String, TableInfo.Column>(19);
        _columnsWishlistItems.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("itemName", new TableInfo.Column("itemName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("estimatedPrice", new TableInfo.Column("estimatedPrice", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("merchant", new TableInfo.Column("merchant", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("addedDate", new TableInfo.Column("addedDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("delayPeriodHours", new TableInfo.Column("delayPeriodHours", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("isDelayActive", new TableInfo.Column("isDelayActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("delayEndTime", new TableInfo.Column("delayEndTime", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("priority", new TableInfo.Column("priority", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("tags", new TableInfo.Column("tags", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("imageUrl", new TableInfo.Column("imageUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("productUrl", new TableInfo.Column("productUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("isPurchased", new TableInfo.Column("isPurchased", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("purchasedDate", new TableInfo.Column("purchasedDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("actualPrice", new TableInfo.Column("actualPrice", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("reflectionNotes", new TableInfo.Column("reflectionNotes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishlistItems.put("stillWanted", new TableInfo.Column("stillWanted", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysWishlistItems = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesWishlistItems = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoWishlistItems = new TableInfo("wishlist_items", _columnsWishlistItems, _foreignKeysWishlistItems, _indicesWishlistItems);
        final TableInfo _existingWishlistItems = TableInfo.read(db, "wishlist_items");
        if (!_infoWishlistItems.equals(_existingWishlistItems)) {
          return new RoomOpenHelper.ValidationResult(false, "wishlist_items(com.focusflow.data.model.WishlistItem).\n"
                  + " Expected:\n" + _infoWishlistItems + "\n"
                  + " Found:\n" + _existingWishlistItems);
        }
        final HashMap<String, TableInfo.Column> _columnsBudgetRecommendations = new HashMap<String, TableInfo.Column>(15);
        _columnsBudgetRecommendations.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("categoryName", new TableInfo.Column("categoryName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("recommendedAmount", new TableInfo.Column("recommendedAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("currentAmount", new TableInfo.Column("currentAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("confidenceScore", new TableInfo.Column("confidenceScore", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("reasonCode", new TableInfo.Column("reasonCode", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("reasonDescription", new TableInfo.Column("reasonDescription", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("generatedDate", new TableInfo.Column("generatedDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("isAccepted", new TableInfo.Column("isAccepted", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("userFeedback", new TableInfo.Column("userFeedback", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("basedOnDays", new TableInfo.Column("basedOnDays", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("seasonalFactor", new TableInfo.Column("seasonalFactor", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("trendFactor", new TableInfo.Column("trendFactor", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("varianceFactor", new TableInfo.Column("varianceFactor", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetRecommendations.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysBudgetRecommendations = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesBudgetRecommendations = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoBudgetRecommendations = new TableInfo("budget_recommendations", _columnsBudgetRecommendations, _foreignKeysBudgetRecommendations, _indicesBudgetRecommendations);
        final TableInfo _existingBudgetRecommendations = TableInfo.read(db, "budget_recommendations");
        if (!_infoBudgetRecommendations.equals(_existingBudgetRecommendations)) {
          return new RoomOpenHelper.ValidationResult(false, "budget_recommendations(com.focusflow.data.model.BudgetRecommendation).\n"
                  + " Expected:\n" + _infoBudgetRecommendations + "\n"
                  + " Found:\n" + _existingBudgetRecommendations);
        }
        final HashMap<String, TableInfo.Column> _columnsSpendingReflections = new HashMap<String, TableInfo.Column>(18);
        _columnsSpendingReflections.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("expenseId", new TableInfo.Column("expenseId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("wishlistItemId", new TableInfo.Column("wishlistItemId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("amount", new TableInfo.Column("amount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("itemDescription", new TableInfo.Column("itemDescription", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("reflectionDate", new TableInfo.Column("reflectionDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("emotionalState", new TableInfo.Column("emotionalState", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("triggerReason", new TableInfo.Column("triggerReason", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("needVsWant", new TableInfo.Column("needVsWant", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("satisfactionLevel", new TableInfo.Column("satisfactionLevel", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("regretLevel", new TableInfo.Column("regretLevel", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("wouldBuyAgain", new TableInfo.Column("wouldBuyAgain", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("alternativeConsidered", new TableInfo.Column("alternativeConsidered", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("delayHelpful", new TableInfo.Column("delayHelpful", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("mindfulnessScore", new TableInfo.Column("mindfulnessScore", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingReflections.put("budgetImpact", new TableInfo.Column("budgetImpact", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSpendingReflections = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSpendingReflections = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSpendingReflections = new TableInfo("spending_reflections", _columnsSpendingReflections, _foreignKeysSpendingReflections, _indicesSpendingReflections);
        final TableInfo _existingSpendingReflections = TableInfo.read(db, "spending_reflections");
        if (!_infoSpendingReflections.equals(_existingSpendingReflections)) {
          return new RoomOpenHelper.ValidationResult(false, "spending_reflections(com.focusflow.data.model.SpendingReflection).\n"
                  + " Expected:\n" + _infoSpendingReflections + "\n"
                  + " Found:\n" + _existingSpendingReflections);
        }
        final HashMap<String, TableInfo.Column> _columnsBudgetAnalytics = new HashMap<String, TableInfo.Column>(22);
        _columnsBudgetAnalytics.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("categoryName", new TableInfo.Column("categoryName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("budgetPeriod", new TableInfo.Column("budgetPeriod", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("budgetYear", new TableInfo.Column("budgetYear", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("budgetMonth", new TableInfo.Column("budgetMonth", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("budgetWeek", new TableInfo.Column("budgetWeek", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("plannedAmount", new TableInfo.Column("plannedAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("actualSpent", new TableInfo.Column("actualSpent", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("variance", new TableInfo.Column("variance", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("variancePercentage", new TableInfo.Column("variancePercentage", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("trendDirection", new TableInfo.Column("trendDirection", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("averageTransactionSize", new TableInfo.Column("averageTransactionSize", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("transactionCount", new TableInfo.Column("transactionCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("largestTransaction", new TableInfo.Column("largestTransaction", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("smallestTransaction", new TableInfo.Column("smallestTransaction", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("mostFrequentMerchant", new TableInfo.Column("mostFrequentMerchant", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("calculatedDate", new TableInfo.Column("calculatedDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("daysInPeriod", new TableInfo.Column("daysInPeriod", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("projectedEndAmount", new TableInfo.Column("projectedEndAmount", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("recommendedAdjustment", new TableInfo.Column("recommendedAdjustment", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("seasonalityFactor", new TableInfo.Column("seasonalityFactor", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBudgetAnalytics.put("isOutlierPeriod", new TableInfo.Column("isOutlierPeriod", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysBudgetAnalytics = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesBudgetAnalytics = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoBudgetAnalytics = new TableInfo("budget_analytics", _columnsBudgetAnalytics, _foreignKeysBudgetAnalytics, _indicesBudgetAnalytics);
        final TableInfo _existingBudgetAnalytics = TableInfo.read(db, "budget_analytics");
        if (!_infoBudgetAnalytics.equals(_existingBudgetAnalytics)) {
          return new RoomOpenHelper.ValidationResult(false, "budget_analytics(com.focusflow.data.model.BudgetAnalytics).\n"
                  + " Expected:\n" + _infoBudgetAnalytics + "\n"
                  + " Found:\n" + _existingBudgetAnalytics);
        }
        final HashMap<String, TableInfo.Column> _columnsFocusSessions = new HashMap<String, TableInfo.Column>(26);
        _columnsFocusSessions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("taskType", new TableInfo.Column("taskType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("sessionName", new TableInfo.Column("sessionName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("plannedDurationMinutes", new TableInfo.Column("plannedDurationMinutes", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("actualDurationMinutes", new TableInfo.Column("actualDurationMinutes", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("startTime", new TableInfo.Column("startTime", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("endTime", new TableInfo.Column("endTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("wasInterrupted", new TableInfo.Column("wasInterrupted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("interruptionCount", new TableInfo.Column("interruptionCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("breaksTaken", new TableInfo.Column("breaksTaken", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("focusQuality", new TableInfo.Column("focusQuality", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("productivityScore", new TableInfo.Column("productivityScore", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("tasksCompleted", new TableInfo.Column("tasksCompleted", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("distractions", new TableInfo.Column("distractions", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("sessionGoal", new TableInfo.Column("sessionGoal", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("goalAchieved", new TableInfo.Column("goalAchieved", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("energyLevelBefore", new TableInfo.Column("energyLevelBefore", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("energyLevelAfter", new TableInfo.Column("energyLevelAfter", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("moodBefore", new TableInfo.Column("moodBefore", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("moodAfter", new TableInfo.Column("moodAfter", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("pomodoroCount", new TableInfo.Column("pomodoroCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("sessionType", new TableInfo.Column("sessionType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("backgroundSound", new TableInfo.Column("backgroundSound", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFocusSessions.put("isSuccessful", new TableInfo.Column("isSuccessful", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysFocusSessions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesFocusSessions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoFocusSessions = new TableInfo("focus_sessions", _columnsFocusSessions, _foreignKeysFocusSessions, _indicesFocusSessions);
        final TableInfo _existingFocusSessions = TableInfo.read(db, "focus_sessions");
        if (!_infoFocusSessions.equals(_existingFocusSessions)) {
          return new RoomOpenHelper.ValidationResult(false, "focus_sessions(com.focusflow.data.model.FocusSession).\n"
                  + " Expected:\n" + _infoFocusSessions + "\n"
                  + " Found:\n" + _existingFocusSessions);
        }
        final HashMap<String, TableInfo.Column> _columnsSpendingPatterns = new HashMap<String, TableInfo.Column>(28);
        _columnsSpendingPatterns.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("patternType", new TableInfo.Column("patternType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("patternName", new TableInfo.Column("patternName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("triggerConditions", new TableInfo.Column("triggerConditions", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("averageAmount", new TableInfo.Column("averageAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("frequency", new TableInfo.Column("frequency", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("emotionalTriggers", new TableInfo.Column("emotionalTriggers", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("timePatterns", new TableInfo.Column("timePatterns", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("locationTriggers", new TableInfo.Column("locationTriggers", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("socialTriggers", new TableInfo.Column("socialTriggers", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("detectedDate", new TableInfo.Column("detectedDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("lastOccurrence", new TableInfo.Column("lastOccurrence", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("occurrenceCount", new TableInfo.Column("occurrenceCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("confidenceScore", new TableInfo.Column("confidenceScore", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("userConfirmed", new TableInfo.Column("userConfirmed", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("interventionStrategy", new TableInfo.Column("interventionStrategy", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("interventionEffectiveness", new TableInfo.Column("interventionEffectiveness", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("severity", new TableInfo.Column("severity", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("impactOnBudget", new TableInfo.Column("impactOnBudget", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("relatedPatterns", new TableInfo.Column("relatedPatterns", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("seasonalFactor", new TableInfo.Column("seasonalFactor", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("stressLevel", new TableInfo.Column("stressLevel", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("preventionSuccess", new TableInfo.Column("preventionSuccess", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSpendingPatterns.put("preventionAttempts", new TableInfo.Column("preventionAttempts", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSpendingPatterns = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSpendingPatterns = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSpendingPatterns = new TableInfo("spending_patterns", _columnsSpendingPatterns, _foreignKeysSpendingPatterns, _indicesSpendingPatterns);
        final TableInfo _existingSpendingPatterns = TableInfo.read(db, "spending_patterns");
        if (!_infoSpendingPatterns.equals(_existingSpendingPatterns)) {
          return new RoomOpenHelper.ValidationResult(false, "spending_patterns(com.focusflow.data.model.SpendingPattern).\n"
                  + " Expected:\n" + _infoSpendingPatterns + "\n"
                  + " Found:\n" + _existingSpendingPatterns);
        }
        final HashMap<String, TableInfo.Column> _columnsAlternativeProducts = new HashMap<String, TableInfo.Column>(36);
        _columnsAlternativeProducts.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("originalProductName", new TableInfo.Column("originalProductName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("originalPrice", new TableInfo.Column("originalPrice", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("originalCategory", new TableInfo.Column("originalCategory", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("alternativeName", new TableInfo.Column("alternativeName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("alternativePrice", new TableInfo.Column("alternativePrice", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("alternativeCategory", new TableInfo.Column("alternativeCategory", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("alternativeType", new TableInfo.Column("alternativeType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("savingsAmount", new TableInfo.Column("savingsAmount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("savingsPercentage", new TableInfo.Column("savingsPercentage", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("pros", new TableInfo.Column("pros", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("cons", new TableInfo.Column("cons", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("availabilityInfo", new TableInfo.Column("availabilityInfo", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("qualityRating", new TableInfo.Column("qualityRating", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("userRating", new TableInfo.Column("userRating", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("suggestionSource", new TableInfo.Column("suggestionSource", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("confidenceScore", new TableInfo.Column("confidenceScore", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("createdDate", new TableInfo.Column("createdDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("lastSuggested", new TableInfo.Column("lastSuggested", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("timesShown", new TableInfo.Column("timesShown", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("timesAccepted", new TableInfo.Column("timesAccepted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("timesRejected", new TableInfo.Column("timesRejected", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("userFeedback", new TableInfo.Column("userFeedback", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("tags", new TableInfo.Column("tags", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("imageUrl", new TableInfo.Column("imageUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("productUrl", new TableInfo.Column("productUrl", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("merchant", new TableInfo.Column("merchant", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("estimatedDeliveryTime", new TableInfo.Column("estimatedDeliveryTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("sustainabilityScore", new TableInfo.Column("sustainabilityScore", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("difficultyLevel", new TableInfo.Column("difficultyLevel", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("timeInvestment", new TableInfo.Column("timeInvestment", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("requiredSkills", new TableInfo.Column("requiredSkills", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("successRate", new TableInfo.Column("successRate", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAlternativeProducts.put("relatedAlternatives", new TableInfo.Column("relatedAlternatives", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAlternativeProducts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAlternativeProducts = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAlternativeProducts = new TableInfo("alternative_products", _columnsAlternativeProducts, _foreignKeysAlternativeProducts, _indicesAlternativeProducts);
        final TableInfo _existingAlternativeProducts = TableInfo.read(db, "alternative_products");
        if (!_infoAlternativeProducts.equals(_existingAlternativeProducts)) {
          return new RoomOpenHelper.ValidationResult(false, "alternative_products(com.focusflow.data.model.AlternativeProduct).\n"
                  + " Expected:\n" + _infoAlternativeProducts + "\n"
                  + " Found:\n" + _existingAlternativeProducts);
        }
        final HashMap<String, TableInfo.Column> _columnsAccountabilityContacts = new HashMap<String, TableInfo.Column>(32);
        _columnsAccountabilityContacts.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("relationship", new TableInfo.Column("relationship", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("contactMethod", new TableInfo.Column("contactMethod", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("contactInfo", new TableInfo.Column("contactInfo", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("permissionLevel", new TableInfo.Column("permissionLevel", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("sharingPreferences", new TableInfo.Column("sharingPreferences", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("addedDate", new TableInfo.Column("addedDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("lastContactDate", new TableInfo.Column("lastContactDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("totalInteractions", new TableInfo.Column("totalInteractions", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("successfulInterventions", new TableInfo.Column("successfulInterventions", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("trustLevel", new TableInfo.Column("trustLevel", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("responseRate", new TableInfo.Column("responseRate", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("averageResponseTime", new TableInfo.Column("averageResponseTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("preferredContactTime", new TableInfo.Column("preferredContactTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("timeZone", new TableInfo.Column("timeZone", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("notificationFrequency", new TableInfo.Column("notificationFrequency", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("supportType", new TableInfo.Column("supportType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("specializations", new TableInfo.Column("specializations", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("emergencyContact", new TableInfo.Column("emergencyContact", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("canReceiveSpendingAlerts", new TableInfo.Column("canReceiveSpendingAlerts", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("canReceiveBudgetUpdates", new TableInfo.Column("canReceiveBudgetUpdates", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("canReceiveGoalProgress", new TableInfo.Column("canReceiveGoalProgress", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("canReceiveEmergencyAlerts", new TableInfo.Column("canReceiveEmergencyAlerts", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("mutualAccountability", new TableInfo.Column("mutualAccountability", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("partnerUserId", new TableInfo.Column("partnerUserId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("consentGiven", new TableInfo.Column("consentGiven", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("consentDate", new TableInfo.Column("consentDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("lastConsentUpdate", new TableInfo.Column("lastConsentUpdate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAccountabilityContacts.put("privacyLevel", new TableInfo.Column("privacyLevel", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAccountabilityContacts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAccountabilityContacts = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAccountabilityContacts = new TableInfo("accountability_contacts", _columnsAccountabilityContacts, _foreignKeysAccountabilityContacts, _indicesAccountabilityContacts);
        final TableInfo _existingAccountabilityContacts = TableInfo.read(db, "accountability_contacts");
        if (!_infoAccountabilityContacts.equals(_existingAccountabilityContacts)) {
          return new RoomOpenHelper.ValidationResult(false, "accountability_contacts(com.focusflow.data.model.AccountabilityContact).\n"
                  + " Expected:\n" + _infoAccountabilityContacts + "\n"
                  + " Found:\n" + _existingAccountabilityContacts);
        }
        final HashMap<String, TableInfo.Column> _columnsDashboardWidgets = new HashMap<String, TableInfo.Column>(39);
        _columnsDashboardWidgets.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("widgetType", new TableInfo.Column("widgetType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("displayName", new TableInfo.Column("displayName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("position", new TableInfo.Column("position", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("isVisible", new TableInfo.Column("isVisible", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("isEnabled", new TableInfo.Column("isEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("size", new TableInfo.Column("size", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("configuration", new TableInfo.Column("configuration", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("refreshInterval", new TableInfo.Column("refreshInterval", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("lastUpdated", new TableInfo.Column("lastUpdated", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("dataSource", new TableInfo.Column("dataSource", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("customTitle", new TableInfo.Column("customTitle", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("colorScheme", new TableInfo.Column("colorScheme", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("customColors", new TableInfo.Column("customColors", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("showHeader", new TableInfo.Column("showHeader", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("showFooter", new TableInfo.Column("showFooter", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("headerText", new TableInfo.Column("headerText", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("footerText", new TableInfo.Column("footerText", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("iconName", new TableInfo.Column("iconName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("animationEnabled", new TableInfo.Column("animationEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("clickAction", new TableInfo.Column("clickAction", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("longPressAction", new TableInfo.Column("longPressAction", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("swipeActions", new TableInfo.Column("swipeActions", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("accessibilityLabel", new TableInfo.Column("accessibilityLabel", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("accessibilityHint", new TableInfo.Column("accessibilityHint", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("isCustomizable", new TableInfo.Column("isCustomizable", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("requiresPermission", new TableInfo.Column("requiresPermission", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("permissionType", new TableInfo.Column("permissionType", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("dataRetentionDays", new TableInfo.Column("dataRetentionDays", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("cacheEnabled", new TableInfo.Column("cacheEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("offlineSupport", new TableInfo.Column("offlineSupport", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("errorFallback", new TableInfo.Column("errorFallback", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("loadingIndicator", new TableInfo.Column("loadingIndicator", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("updateAnimation", new TableInfo.Column("updateAnimation", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("priority", new TableInfo.Column("priority", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("dependencies", new TableInfo.Column("dependencies", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("createdDate", new TableInfo.Column("createdDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("modifiedDate", new TableInfo.Column("modifiedDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDashboardWidgets.put("userNotes", new TableInfo.Column("userNotes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysDashboardWidgets = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesDashboardWidgets = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoDashboardWidgets = new TableInfo("dashboard_widgets", _columnsDashboardWidgets, _foreignKeysDashboardWidgets, _indicesDashboardWidgets);
        final TableInfo _existingDashboardWidgets = TableInfo.read(db, "dashboard_widgets");
        if (!_infoDashboardWidgets.equals(_existingDashboardWidgets)) {
          return new RoomOpenHelper.ValidationResult(false, "dashboard_widgets(com.focusflow.data.model.DashboardWidget).\n"
                  + " Expected:\n" + _infoDashboardWidgets + "\n"
                  + " Found:\n" + _existingDashboardWidgets);
        }
        final HashMap<String, TableInfo.Column> _columnsVoiceCommands = new HashMap<String, TableInfo.Column>(32);
        _columnsVoiceCommands.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("commandText", new TableInfo.Column("commandText", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("recognizedText", new TableInfo.Column("recognizedText", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("commandType", new TableInfo.Column("commandType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("intent", new TableInfo.Column("intent", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("parameters", new TableInfo.Column("parameters", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("isSuccessful", new TableInfo.Column("isSuccessful", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("confidence", new TableInfo.Column("confidence", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("processingTime", new TableInfo.Column("processingTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("timestamp", new TableInfo.Column("timestamp", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("sessionId", new TableInfo.Column("sessionId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("context", new TableInfo.Column("context", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("followUpRequired", new TableInfo.Column("followUpRequired", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("followUpPrompt", new TableInfo.Column("followUpPrompt", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("errorMessage", new TableInfo.Column("errorMessage", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("correctedCommand", new TableInfo.Column("correctedCommand", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("actionTaken", new TableInfo.Column("actionTaken", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("resultData", new TableInfo.Column("resultData", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("userFeedback", new TableInfo.Column("userFeedback", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("language", new TableInfo.Column("language", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("accent", new TableInfo.Column("accent", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("noiseLevel", new TableInfo.Column("noiseLevel", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("deviceType", new TableInfo.Column("deviceType", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("isOffline", new TableInfo.Column("isOffline", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("retryCount", new TableInfo.Column("retryCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("alternativeCommands", new TableInfo.Column("alternativeCommands", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("learningData", new TableInfo.Column("learningData", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("privacyLevel", new TableInfo.Column("privacyLevel", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("isTrainingData", new TableInfo.Column("isTrainingData", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("customVocabulary", new TableInfo.Column("customVocabulary", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVoiceCommands.put("shortcuts", new TableInfo.Column("shortcuts", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysVoiceCommands = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesVoiceCommands = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoVoiceCommands = new TableInfo("voice_commands", _columnsVoiceCommands, _foreignKeysVoiceCommands, _indicesVoiceCommands);
        final TableInfo _existingVoiceCommands = TableInfo.read(db, "voice_commands");
        if (!_infoVoiceCommands.equals(_existingVoiceCommands)) {
          return new RoomOpenHelper.ValidationResult(false, "voice_commands(com.focusflow.data.model.VoiceCommand).\n"
                  + " Expected:\n" + _infoVoiceCommands + "\n"
                  + " Found:\n" + _existingVoiceCommands);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "688b39ab097885dd242d4141f16d16aa", "9a3b8544ae8dfa0abf86192c64ba7396");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "expenses","credit_cards","budget_categories","habit_logs","tasks","ai_interactions","user_preferences","achievements","user_stats","virtual_pet","wishlist_items","budget_recommendations","spending_reflections","budget_analytics","focus_sessions","spending_patterns","alternative_products","accountability_contacts","dashboard_widgets","voice_commands");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `expenses`");
      _db.execSQL("DELETE FROM `credit_cards`");
      _db.execSQL("DELETE FROM `budget_categories`");
      _db.execSQL("DELETE FROM `habit_logs`");
      _db.execSQL("DELETE FROM `tasks`");
      _db.execSQL("DELETE FROM `ai_interactions`");
      _db.execSQL("DELETE FROM `user_preferences`");
      _db.execSQL("DELETE FROM `achievements`");
      _db.execSQL("DELETE FROM `user_stats`");
      _db.execSQL("DELETE FROM `virtual_pet`");
      _db.execSQL("DELETE FROM `wishlist_items`");
      _db.execSQL("DELETE FROM `budget_recommendations`");
      _db.execSQL("DELETE FROM `spending_reflections`");
      _db.execSQL("DELETE FROM `budget_analytics`");
      _db.execSQL("DELETE FROM `focus_sessions`");
      _db.execSQL("DELETE FROM `spending_patterns`");
      _db.execSQL("DELETE FROM `alternative_products`");
      _db.execSQL("DELETE FROM `accountability_contacts`");
      _db.execSQL("DELETE FROM `dashboard_widgets`");
      _db.execSQL("DELETE FROM `voice_commands`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(ExpenseDao.class, ExpenseDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CreditCardDao.class, CreditCardDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(BudgetCategoryDao.class, BudgetCategoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(HabitLogDao.class, HabitLogDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TaskDao.class, TaskDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AIInteractionDao.class, AIInteractionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserPreferencesDao.class, UserPreferencesDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AchievementDao.class, AchievementDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserStatsDao.class, UserStatsDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(VirtualPetDao.class, VirtualPetDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(WishlistItemDao.class, WishlistItemDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(BudgetRecommendationDao.class, BudgetRecommendationDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SpendingReflectionDao.class, SpendingReflectionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(BudgetAnalyticsDao.class, BudgetAnalyticsDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(FocusSessionDao.class, FocusSessionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SpendingPatternDao.class, SpendingPatternDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AlternativeProductDao.class, AlternativeProductDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AccountabilityContactDao.class, AccountabilityContactDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(DashboardWidgetDao.class, DashboardWidgetDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(VoiceCommandDao.class, VoiceCommandDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public ExpenseDao expenseDao() {
    if (_expenseDao != null) {
      return _expenseDao;
    } else {
      synchronized(this) {
        if(_expenseDao == null) {
          _expenseDao = new ExpenseDao_Impl(this);
        }
        return _expenseDao;
      }
    }
  }

  @Override
  public CreditCardDao creditCardDao() {
    if (_creditCardDao != null) {
      return _creditCardDao;
    } else {
      synchronized(this) {
        if(_creditCardDao == null) {
          _creditCardDao = new CreditCardDao_Impl(this);
        }
        return _creditCardDao;
      }
    }
  }

  @Override
  public BudgetCategoryDao budgetCategoryDao() {
    if (_budgetCategoryDao != null) {
      return _budgetCategoryDao;
    } else {
      synchronized(this) {
        if(_budgetCategoryDao == null) {
          _budgetCategoryDao = new BudgetCategoryDao_Impl(this);
        }
        return _budgetCategoryDao;
      }
    }
  }

  @Override
  public HabitLogDao habitLogDao() {
    if (_habitLogDao != null) {
      return _habitLogDao;
    } else {
      synchronized(this) {
        if(_habitLogDao == null) {
          _habitLogDao = new HabitLogDao_Impl(this);
        }
        return _habitLogDao;
      }
    }
  }

  @Override
  public TaskDao taskDao() {
    if (_taskDao != null) {
      return _taskDao;
    } else {
      synchronized(this) {
        if(_taskDao == null) {
          _taskDao = new TaskDao_Impl(this);
        }
        return _taskDao;
      }
    }
  }

  @Override
  public AIInteractionDao aiInteractionDao() {
    if (_aIInteractionDao != null) {
      return _aIInteractionDao;
    } else {
      synchronized(this) {
        if(_aIInteractionDao == null) {
          _aIInteractionDao = new AIInteractionDao_Impl(this);
        }
        return _aIInteractionDao;
      }
    }
  }

  @Override
  public UserPreferencesDao userPreferencesDao() {
    if (_userPreferencesDao != null) {
      return _userPreferencesDao;
    } else {
      synchronized(this) {
        if(_userPreferencesDao == null) {
          _userPreferencesDao = new UserPreferencesDao_Impl(this);
        }
        return _userPreferencesDao;
      }
    }
  }

  @Override
  public AchievementDao achievementDao() {
    if (_achievementDao != null) {
      return _achievementDao;
    } else {
      synchronized(this) {
        if(_achievementDao == null) {
          _achievementDao = new AchievementDao_Impl(this);
        }
        return _achievementDao;
      }
    }
  }

  @Override
  public UserStatsDao userStatsDao() {
    if (_userStatsDao != null) {
      return _userStatsDao;
    } else {
      synchronized(this) {
        if(_userStatsDao == null) {
          _userStatsDao = new UserStatsDao_Impl(this);
        }
        return _userStatsDao;
      }
    }
  }

  @Override
  public VirtualPetDao virtualPetDao() {
    if (_virtualPetDao != null) {
      return _virtualPetDao;
    } else {
      synchronized(this) {
        if(_virtualPetDao == null) {
          _virtualPetDao = new VirtualPetDao_Impl(this);
        }
        return _virtualPetDao;
      }
    }
  }

  @Override
  public WishlistItemDao wishlistItemDao() {
    if (_wishlistItemDao != null) {
      return _wishlistItemDao;
    } else {
      synchronized(this) {
        if(_wishlistItemDao == null) {
          _wishlistItemDao = new WishlistItemDao_Impl(this);
        }
        return _wishlistItemDao;
      }
    }
  }

  @Override
  public BudgetRecommendationDao budgetRecommendationDao() {
    if (_budgetRecommendationDao != null) {
      return _budgetRecommendationDao;
    } else {
      synchronized(this) {
        if(_budgetRecommendationDao == null) {
          _budgetRecommendationDao = new BudgetRecommendationDao_Impl(this);
        }
        return _budgetRecommendationDao;
      }
    }
  }

  @Override
  public SpendingReflectionDao spendingReflectionDao() {
    if (_spendingReflectionDao != null) {
      return _spendingReflectionDao;
    } else {
      synchronized(this) {
        if(_spendingReflectionDao == null) {
          _spendingReflectionDao = new SpendingReflectionDao_Impl(this);
        }
        return _spendingReflectionDao;
      }
    }
  }

  @Override
  public BudgetAnalyticsDao budgetAnalyticsDao() {
    if (_budgetAnalyticsDao != null) {
      return _budgetAnalyticsDao;
    } else {
      synchronized(this) {
        if(_budgetAnalyticsDao == null) {
          _budgetAnalyticsDao = new BudgetAnalyticsDao_Impl(this);
        }
        return _budgetAnalyticsDao;
      }
    }
  }

  @Override
  public FocusSessionDao focusSessionDao() {
    if (_focusSessionDao != null) {
      return _focusSessionDao;
    } else {
      synchronized(this) {
        if(_focusSessionDao == null) {
          _focusSessionDao = new FocusSessionDao_Impl(this);
        }
        return _focusSessionDao;
      }
    }
  }

  @Override
  public SpendingPatternDao spendingPatternDao() {
    if (_spendingPatternDao != null) {
      return _spendingPatternDao;
    } else {
      synchronized(this) {
        if(_spendingPatternDao == null) {
          _spendingPatternDao = new SpendingPatternDao_Impl(this);
        }
        return _spendingPatternDao;
      }
    }
  }

  @Override
  public AlternativeProductDao alternativeProductDao() {
    if (_alternativeProductDao != null) {
      return _alternativeProductDao;
    } else {
      synchronized(this) {
        if(_alternativeProductDao == null) {
          _alternativeProductDao = new AlternativeProductDao_Impl(this);
        }
        return _alternativeProductDao;
      }
    }
  }

  @Override
  public AccountabilityContactDao accountabilityContactDao() {
    if (_accountabilityContactDao != null) {
      return _accountabilityContactDao;
    } else {
      synchronized(this) {
        if(_accountabilityContactDao == null) {
          _accountabilityContactDao = new AccountabilityContactDao_Impl(this);
        }
        return _accountabilityContactDao;
      }
    }
  }

  @Override
  public DashboardWidgetDao dashboardWidgetDao() {
    if (_dashboardWidgetDao != null) {
      return _dashboardWidgetDao;
    } else {
      synchronized(this) {
        if(_dashboardWidgetDao == null) {
          _dashboardWidgetDao = new DashboardWidgetDao_Impl(this);
        }
        return _dashboardWidgetDao;
      }
    }
  }

  @Override
  public VoiceCommandDao voiceCommandDao() {
    if (_voiceCommandDao != null) {
      return _voiceCommandDao;
    } else {
      synchronized(this) {
        if(_voiceCommandDao == null) {
          _voiceCommandDao = new VoiceCommandDao_Impl(this);
        }
        return _voiceCommandDao;
      }
    }
  }
}
