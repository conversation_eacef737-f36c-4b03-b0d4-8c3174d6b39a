package com.focusflow.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.DebtViewModel
import com.focusflow.ui.viewmodel.PayoffStrategy
import com.focusflow.ui.viewmodel.PayoffPlan
import com.focusflow.ui.viewmodel.PayoffStep
import kotlinx.datetime.*
import kotlin.math.ceil

// Goal types for payoff planning
enum class PayoffGoalType {
    EXTRA_PAYMENT, // Pay extra amount per month
    TARGET_DATE,   // Pay off by specific date
    TARGET_PAYMENT // Pay specific total amount per month
}

@Composable
fun PayoffPlannerScreen(
    onNavigateBack: () -> Unit,
    viewModel: DebtViewModel = hiltViewModel()
) {
    val creditCards by viewModel.allCreditCards.collectAsStateWithLifecycle(initialValue = emptyList())
    val payoffPlan by viewModel.payoffPlan.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    var selectedStrategy by remember { mutableStateOf(PayoffStrategy.AVALANCHE) }
    var extraPayment by remember { mutableStateOf("") }
    var showComparison by remember { mutableStateOf(false) }
    var goalType by remember { mutableStateOf(PayoffGoalType.EXTRA_PAYMENT) }
    var targetDate by remember { mutableStateOf("") }
    var targetPayment by remember { mutableStateOf("") }
    var showAdvancedOptions by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(Icons.Default.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Debt Payoff Planner",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
            IconButton(
                onClick = { showComparison = !showComparison }
            ) {
                Icon(
                    if (showComparison) Icons.Default.Close else Icons.Default.Add,
                    contentDescription = "Toggle comparison"
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        if (creditCards.isEmpty()) {
            EmptyStateMessage()
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    // Goal Selection Card
                    GoalSelectionCard(
                        goalType = goalType,
                        onGoalTypeChanged = { goalType = it },
                        targetDate = targetDate,
                        onTargetDateChanged = { targetDate = it },
                        targetPayment = targetPayment,
                        onTargetPaymentChanged = { targetPayment = it },
                        extraPayment = extraPayment,
                        onExtraPaymentChanged = { extraPayment = it }
                    )
                }

                item {
                    // Enhanced Strategy Selection
                    EnhancedStrategySelectionCard(
                        selectedStrategy = selectedStrategy,
                        onStrategySelected = { selectedStrategy = it },
                        showAdvancedOptions = showAdvancedOptions,
                        onToggleAdvancedOptions = { showAdvancedOptions = !showAdvancedOptions },
                        onGeneratePlan = {
                            val extra = calculateExtraPayment(
                                goalType = goalType,
                                extraPayment = extraPayment,
                                targetDate = targetDate,
                                targetPayment = targetPayment,
                                creditCards = creditCards
                            )
                            viewModel.generatePayoffPlan(selectedStrategy, extra)
                        }
                    )
                }

                // Results
                if (payoffPlan.steps.isNotEmpty()) {
                    item {
                        EnhancedPayoffResultsSection(
                            payoffPlan = payoffPlan,
                            showComparison = showComparison,
                            creditCards = creditCards,
                            goalType = goalType,
                            viewModel = viewModel
                        )
                    }
                }
            }
        }

        // Error handling
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // TODO: Show snackbar
                viewModel.clearError()
            }
        }
    }
}

// Helper function to calculate extra payment based on goal type
private fun calculateExtraPayment(
    goalType: PayoffGoalType,
    extraPayment: String,
    targetDate: String,
    targetPayment: String,
    creditCards: List<com.focusflow.data.model.CreditCard>
): Double {
    return when (goalType) {
        PayoffGoalType.EXTRA_PAYMENT -> extraPayment.toDoubleOrNull() ?: 0.0
        PayoffGoalType.TARGET_DATE -> {
            // Calculate required extra payment to meet target date
            val totalDebt = creditCards.sumOf { it.currentBalance }
            val totalMinPayments = creditCards.sumOf { it.minimumPayment }
            val monthsToTarget = targetDate.toIntOrNull() ?: 12

            if (monthsToTarget > 0 && totalDebt > 0) {
                val requiredMonthlyPayment = totalDebt / monthsToTarget
                (requiredMonthlyPayment - totalMinPayments).coerceAtLeast(0.0)
            } else 0.0
        }
        PayoffGoalType.TARGET_PAYMENT -> {
            val totalMinPayments = creditCards.sumOf { it.minimumPayment }
            val targetTotal = targetPayment.toDoubleOrNull() ?: totalMinPayments
            (targetTotal - totalMinPayments).coerceAtLeast(0.0)
        }
    }
}

@Composable
fun GoalSelectionCard(
    goalType: PayoffGoalType,
    onGoalTypeChanged: (PayoffGoalType) -> Unit,
    targetDate: String,
    onTargetDateChanged: (String) -> Unit,
    targetPayment: String,
    onTargetPaymentChanged: (String) -> Unit,
    extraPayment: String,
    onExtraPaymentChanged: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.05f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Star,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Payoff Goal",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colors.primary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Goal type selection
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                GoalTypeOption(
                    selected = goalType == PayoffGoalType.EXTRA_PAYMENT,
                    onClick = { onGoalTypeChanged(PayoffGoalType.EXTRA_PAYMENT) },
                    title = "Pay Extra Amount",
                    description = "Add extra money to monthly payments",
                    icon = Icons.Default.Add
                )

                GoalTypeOption(
                    selected = goalType == PayoffGoalType.TARGET_DATE,
                    onClick = { onGoalTypeChanged(PayoffGoalType.TARGET_DATE) },
                    title = "Pay Off By Date",
                    description = "Become debt-free by a specific time",
                    icon = Icons.Default.DateRange
                )

                GoalTypeOption(
                    selected = goalType == PayoffGoalType.TARGET_PAYMENT,
                    onClick = { onGoalTypeChanged(PayoffGoalType.TARGET_PAYMENT) },
                    title = "Fixed Monthly Payment",
                    description = "Pay a specific total amount each month",
                    icon = Icons.Default.AccountBox
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Goal-specific input
            when (goalType) {
                PayoffGoalType.EXTRA_PAYMENT -> {
                    OutlinedTextField(
                        value = extraPayment,
                        onValueChange = onExtraPaymentChanged,
                        label = { Text("Extra Monthly Payment") },
                        placeholder = { Text("0.00") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth(),
                        leadingIcon = {
                            Text(
                                text = "$",
                                style = MaterialTheme.typography.body1,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    )
                }
                PayoffGoalType.TARGET_DATE -> {
                    OutlinedTextField(
                        value = targetDate,
                        onValueChange = onTargetDateChanged,
                        label = { Text("Months to Pay Off") },
                        placeholder = { Text("12") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth(),
                        trailingIcon = {
                            Text(
                                text = "months",
                                style = MaterialTheme.typography.caption,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    )
                }
                PayoffGoalType.TARGET_PAYMENT -> {
                    OutlinedTextField(
                        value = targetPayment,
                        onValueChange = onTargetPaymentChanged,
                        label = { Text("Total Monthly Payment") },
                        placeholder = { Text("500.00") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth(),
                        leadingIcon = {
                            Text(
                                text = "$",
                                style = MaterialTheme.typography.body1,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun GoalTypeOption(
    selected: Boolean,
    onClick: () -> Unit,
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = if (selected) 4.dp else 1.dp,
        shape = RoundedCornerShape(8.dp),
        backgroundColor = if (selected) {
            MaterialTheme.colors.primary.copy(alpha = 0.1f)
        } else {
            MaterialTheme.colors.surface
        }
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = MaterialTheme.colors.primary
                )
            )

            Icon(
                icon,
                contentDescription = null,
                tint = if (selected) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium,
                    color = if (selected) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun EmptyStateMessage() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No Credit Cards",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Add credit cards to create a payoff plan",
                style = MaterialTheme.typography.body2,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun EnhancedStrategySelectionCard(
    selectedStrategy: PayoffStrategy,
    onStrategySelected: (PayoffStrategy) -> Unit,
    showAdvancedOptions: Boolean,
    onToggleAdvancedOptions: () -> Unit,
    onGeneratePlan: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Star,
                        contentDescription = null,
                        tint = MaterialTheme.colors.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Payoff Strategy",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold
                    )
                }

                TextButton(
                    onClick = onToggleAdvancedOptions
                ) {
                    Text(
                        text = if (showAdvancedOptions) "Simple" else "Advanced",
                        color = MaterialTheme.colors.primary
                    )
                    Icon(
                        if (showAdvancedOptions) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        tint = MaterialTheme.colors.primary
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Strategy options with enhanced descriptions
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                EnhancedStrategyOption(
                    selected = selectedStrategy == PayoffStrategy.AVALANCHE,
                    onClick = { onStrategySelected(PayoffStrategy.AVALANCHE) },
                    title = "Avalanche Method",
                    subtitle = "Mathematically Optimal",
                    description = "Pay highest interest rate first - saves the most money",
                    icon = Icons.Default.KeyboardArrowDown,
                    color = Color(0xFF4CAF50),
                    showAdvanced = showAdvancedOptions
                )

                EnhancedStrategyOption(
                    selected = selectedStrategy == PayoffStrategy.SNOWBALL,
                    onClick = { onStrategySelected(PayoffStrategy.SNOWBALL) },
                    title = "Snowball Method",
                    subtitle = "Psychologically Motivating",
                    description = "Pay smallest balance first - builds momentum and motivation",
                    icon = Icons.Default.KeyboardArrowUp,
                    color = Color(0xFF2196F3),
                    showAdvanced = showAdvancedOptions
                )
            }

            Spacer(modifier = Modifier.height(20.dp))

            // Generate plan button with enhanced styling
            Button(
                onClick = onGeneratePlan,
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.primary
                )
            ) {
                Icon(
                    Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Generate Payoff Plan",
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
fun EnhancedStrategyOption(
    selected: Boolean,
    onClick: () -> Unit,
    title: String,
    subtitle: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    showAdvanced: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = if (selected) 6.dp else 2.dp,
        shape = RoundedCornerShape(8.dp),
        backgroundColor = if (selected) {
            color.copy(alpha = 0.1f)
        } else {
            MaterialTheme.colors.surface
        }
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = color
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            Icon(
                icon,
                contentDescription = null,
                tint = if (selected) color else MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Bold,
                    color = if (selected) color else MaterialTheme.colors.onSurface
                )

                if (showAdvanced) {
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.caption,
                        fontWeight = FontWeight.Medium,
                        color = color
                    )
                }

                Text(
                    text = description,
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun StrategySelectionCard(
    selectedStrategy: PayoffStrategy,
    onStrategySelected: (PayoffStrategy) -> Unit,
    extraPayment: String,
    onExtraPaymentChanged: (String) -> Unit,
    onGeneratePlan: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Payoff Strategy",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Strategy options
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedStrategy == PayoffStrategy.AVALANCHE,
                        onClick = { onStrategySelected(PayoffStrategy.AVALANCHE) }
                    )
                    Column {
                        Text(
                            text = "Avalanche (Recommended)",
                            style = MaterialTheme.typography.body1,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Pay highest interest rate first - saves more money",
                            style = MaterialTheme.typography.caption,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedStrategy == PayoffStrategy.SNOWBALL,
                        onClick = { onStrategySelected(PayoffStrategy.SNOWBALL) }
                    )
                    Column {
                        Text(
                            text = "Snowball",
                            style = MaterialTheme.typography.body1,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Pay smallest balance first - builds momentum",
                            style = MaterialTheme.typography.caption,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Extra payment input
            OutlinedTextField(
                value = extraPayment,
                onValueChange = onExtraPaymentChanged,
                label = { Text("Extra Monthly Payment") },
                placeholder = { Text("0.00") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = {
                    Text(
                        text = "$",
                        style = MaterialTheme.typography.body1,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Generate plan button
            Button(
                onClick = onGeneratePlan,
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp)
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Generate Payoff Plan")
            }
        }
    }
}

@Composable
fun EnhancedPayoffResultsSection(
    payoffPlan: PayoffPlan,
    showComparison: Boolean,
    creditCards: List<com.focusflow.data.model.CreditCard>,
    goalType: PayoffGoalType,
    viewModel: DebtViewModel
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Enhanced Summary Card
        EnhancedPayoffSummaryCard(
            payoffPlan = payoffPlan,
            goalType = goalType
        )

        // Visual Timeline
        PayoffTimelineCard(payoffPlan = payoffPlan)

        // Strategy Comparison (if enabled)
        if (showComparison) {
            EnhancedStrategyComparisonCard(
                creditCards = creditCards,
                viewModel = viewModel
            )
        }

        // Monthly Schedule
        MonthlyScheduleCard(payoffPlan = payoffPlan)
    }
}

@Composable
fun PayoffResultsSection(
    payoffPlan: PayoffPlan,
    showComparison: Boolean,
    creditCards: List<com.focusflow.data.model.CreditCard>,
    viewModel: DebtViewModel
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            PayoffSummaryCard(payoffPlan = payoffPlan)
        }
        
        if (showComparison) {
            item {
                StrategyComparisonCard(
                    creditCards = creditCards,
                    viewModel = viewModel
                )
            }
        }
        
        item {
            Text(
                text = "Monthly Payment Schedule",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
        }
        
        items(payoffPlan.steps.take(12)) { step -> // Show first 12 months
            PayoffStepItem(step = step)
        }
        
        if (payoffPlan.steps.size > 12) {
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = 2.dp,
                    shape = RoundedCornerShape(8.dp),
                    backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
                ) {
                    Text(
                        text = "... and ${payoffPlan.steps.size - 12} more months",
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.body2,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colors.primary
                    )
                }
            }
        }
    }
}

@Composable
fun PayoffSummaryCard(payoffPlan: PayoffPlan) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Payoff Summary",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.primary
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                SummaryItem(
                    label = "Time to Payoff",
                    value = "${payoffPlan.totalMonths} months",
                    color = MaterialTheme.colors.primary
                )
                SummaryItem(
                    label = "Total Interest",
                    value = "$${String.format("%.2f", payoffPlan.totalInterestPaid)}",
                    color = Color(0xFFFF9800)
                )
                SummaryItem(
                    label = "Total Payments",
                    value = "$${String.format("%.2f", payoffPlan.totalPayments)}",
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
fun SummaryItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        Text(
            text = value,
            style = MaterialTheme.typography.body1,
            fontWeight = FontWeight.Bold,
            color = color,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun StrategyComparisonCard(
    creditCards: List<com.focusflow.data.model.CreditCard>,
    viewModel: DebtViewModel
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Strategy Comparison",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Calculate both strategies for comparison
            val totalDebt = creditCards.sumOf { it.currentBalance }
            val totalMinPayments = creditCards.sumOf { it.minimumPayment }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Avalanche",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colors.primary
                    )
                    Text(
                        text = "Saves more money",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Snowball",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFFFF9800)
                    )
                    Text(
                        text = "Builds momentum",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
fun PayoffStepItem(step: PayoffStep) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp),
        backgroundColor = if (step.isExtraPayment) {
            MaterialTheme.colors.primary.copy(alpha = 0.1f)
        } else {
            MaterialTheme.colors.surface
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Month ${step.month}",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                Text(
                    text = step.cardName,
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Medium
                )
                if (step.interestPaid > 0) {
                    Text(
                        text = "Interest: $${String.format("%.2f", step.interestPaid)}",
                        style = MaterialTheme.typography.caption,
                        color = Color(0xFFFF9800)
                    )
                }
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "$${String.format("%.2f", step.payment)}",
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Bold,
                    color = if (step.isExtraPayment) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface
                )
                Text(
                    text = "Balance: $${String.format("%.2f", step.remainingBalance)}",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

// Enhanced components that are missing
@Composable
fun EnhancedPayoffSummaryCard(
    payoffPlan: PayoffPlan,
    goalType: PayoffGoalType
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 6.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(0xFF4CAF50).copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(28.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Your Payoff Plan",
                    style = MaterialTheme.typography.h5,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF4CAF50)
                )
            }

            Spacer(modifier = Modifier.height(20.dp))

            // Key metrics
            Text(
                text = "You'll be debt-free in ${payoffPlan.totalMonths} months!",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colors.primary
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "Total interest: $${String.format("%.0f", payoffPlan.totalInterestPaid)}",
                style = MaterialTheme.typography.body1,
                color = MaterialTheme.colors.onSurface
            )

            Text(
                text = "Total payments: $${String.format("%.0f", payoffPlan.totalPayments)}",
                style = MaterialTheme.typography.body1,
                color = MaterialTheme.colors.onSurface
            )

            // Goal achievement message
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                backgroundColor = Color(0xFF4CAF50).copy(alpha = 0.2f),
                elevation = 0.dp,
                shape = RoundedCornerShape(8.dp)
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Star,
                        contentDescription = null,
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = getGoalAchievementMessage(goalType, payoffPlan),
                        style = MaterialTheme.typography.body2,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF4CAF50)
                    )
                }
            }
        }
    }
}

@Composable
fun PayoffTimelineCard(payoffPlan: PayoffPlan) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.List,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Payoff Timeline",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Visual timeline with progress indicators
            val totalMonths = payoffPlan.totalMonths
            val milestones = listOf(
                0 to "Start",
                totalMonths / 4 to "25% Complete",
                totalMonths / 2 to "Halfway There!",
                (totalMonths * 3) / 4 to "75% Complete",
                totalMonths to "Debt Free!"
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                milestones.forEach { (month, label) ->
                    TimelineMilestone(
                        month = month,
                        label = label,
                        isStart = month == 0,
                        isEnd = month == totalMonths,
                        totalMonths = totalMonths
                    )
                }
            }
        }
    }
}

@Composable
fun TimelineMilestone(
    month: Int,
    label: String,
    isStart: Boolean,
    isEnd: Boolean,
    totalMonths: Int
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Timeline dot
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(CircleShape)
                .background(
                    when {
                        isStart -> Color(0xFF2196F3)
                        isEnd -> Color(0xFF4CAF50)
                        else -> Color(0xFFFF9800)
                    }
                )
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column {
            Text(
                text = label,
                style = MaterialTheme.typography.body2,
                fontWeight = FontWeight.Medium,
                color = when {
                    isStart -> Color(0xFF2196F3)
                    isEnd -> Color(0xFF4CAF50)
                    else -> MaterialTheme.colors.onSurface
                }
            )

            if (!isStart) {
                Text(
                    text = "Month $month",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun MonthlyScheduleCard(payoffPlan: PayoffPlan) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.List,
                        contentDescription = null,
                        tint = MaterialTheme.colors.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Monthly Schedule",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold
                    )
                }

                Text(
                    text = "${payoffPlan.steps.size} payments",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Show first few steps as preview
            val previewSteps = payoffPlan.steps.take(6)

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                previewSteps.forEach { step ->
                    PayoffStepItem(step = step)
                }

                if (payoffPlan.steps.size > 6) {
                    Card(
                        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f),
                        elevation = 1.dp,
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "... and ${payoffPlan.steps.size - 6} more payments",
                                style = MaterialTheme.typography.body2,
                                color = MaterialTheme.colors.primary,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun EnhancedStrategyComparisonCard(
    creditCards: List<com.focusflow.data.model.CreditCard>,
    viewModel: DebtViewModel
) {
    StrategyComparisonCard(creditCards = creditCards, viewModel = viewModel)
}

// Helper functions
private fun getGoalAchievementMessage(goalType: PayoffGoalType, payoffPlan: PayoffPlan): String {
    return when (goalType) {
        PayoffGoalType.EXTRA_PAYMENT -> "Great! Your extra payments will save you money on interest."
        PayoffGoalType.TARGET_DATE -> "Perfect! You'll be debt-free by your target date."
        PayoffGoalType.TARGET_PAYMENT -> "Excellent! Your fixed payment plan will keep you on track."
    }
}
