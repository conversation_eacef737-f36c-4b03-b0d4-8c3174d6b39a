package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.BudgetCategory
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class ZeroBudgetViewModel @Inject constructor(
    private val budgetCategoryRepository: BudgetCategoryRepository,
    private val expenseRepository: ExpenseRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ZeroBudgetUiState())
    val uiState: StateFlow<ZeroBudgetUiState> = _uiState.asStateFlow()

    init {
        loadZeroBudgetData()
    }

    private fun loadZeroBudgetData() {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"
                val income = if (period == "weekly") preferences?.weeklyIncome else preferences?.monthlyIncome
                
                budgetCategoryRepository.getBudgetCategoriesByPeriod(period)
                    .collect { categories ->
                        val totalAllocated = categories.sumOf { it.allocatedAmount }
                        val totalSpent = categories.sumOf { it.spentAmount }
                        val unallocated = (income ?: 0.0) - totalAllocated
                        
                        _uiState.value = _uiState.value.copy(
                            budgetCategories = categories,
                            totalIncome = income ?: 0.0,
                            totalAllocated = totalAllocated,
                            totalSpent = totalSpent,
                            unallocatedAmount = unallocated,
                            budgetPeriod = period,
                            isBalanced = unallocated == 0.0,
                            isLoading = false
                        )
                    }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load budget data: ${e.message}"
                )
            }
        }
    }

    fun setIncome(amount: Double) {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"
                
                val updatedPrefs = preferences?.copy(
                    weeklyIncome = if (period == "weekly") amount else preferences.weeklyIncome,
                    monthlyIncome = if (period == "monthly") amount else preferences.monthlyIncome,
                    useZeroBasedBudgeting = true
                ) ?: com.focusflow.data.model.UserPreferences(
                    id = 1,
                    budgetPeriod = period,
                    weeklyIncome = if (period == "weekly") amount else null,
                    monthlyIncome = if (period == "monthly") amount else null,
                    useZeroBasedBudgeting = true
                )
                
                userPreferencesRepository.updateUserPreferences(updatedPrefs)
                loadZeroBudgetData() // Refresh data
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to set income: ${e.message}"
                )
            }
        }
    }

    fun addBudgetCategory(name: String, allocatedAmount: Double, color: String = "#2196F3") {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"
                
                val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                val (year, month, week) = getCurrentPeriodValues(now, period)
                
                val budgetCategory = BudgetCategory(
                    name = name,
                    allocatedAmount = allocatedAmount,
                    spentAmount = 0.0,
                    budgetPeriod = period,
                    budgetYear = year,
                    budgetMonth = month,
                    budgetWeek = week,
                    categoryColor = color
                )
                
                budgetCategoryRepository.insertBudgetCategory(budgetCategory)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to add budget category: ${e.message}"
                )
            }
        }
    }

    fun updateCategoryAllocation(categoryId: Long, newAmount: Double) {
        viewModelScope.launch {
            try {
                val category = _uiState.value.budgetCategories.find { it.id == categoryId }
                category?.let {
                    val updatedCategory = it.copy(allocatedAmount = newAmount)
                    budgetCategoryRepository.updateBudgetCategory(updatedCategory)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update allocation: ${e.message}"
                )
            }
        }
    }

    fun transferBetweenEnvelopes(fromCategoryId: Long, toCategoryId: Long, amount: Double) {
        viewModelScope.launch {
            try {
                val fromCategory = _uiState.value.budgetCategories.find { it.id == fromCategoryId }
                val toCategory = _uiState.value.budgetCategories.find { it.id == toCategoryId }
                
                if (fromCategory != null && toCategory != null && fromCategory.allocatedAmount >= amount) {
                    val updatedFromCategory = fromCategory.copy(
                        allocatedAmount = fromCategory.allocatedAmount - amount
                    )
                    val updatedToCategory = toCategory.copy(
                        allocatedAmount = toCategory.allocatedAmount + amount
                    )
                    
                    budgetCategoryRepository.updateBudgetCategory(updatedFromCategory)
                    budgetCategoryRepository.updateBudgetCategory(updatedToCategory)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to transfer funds: ${e.message}"
                )
            }
        }
    }

    fun autoBalanceBudget() {
        viewModelScope.launch {
            try {
                val unallocated = _uiState.value.unallocatedAmount
                val categories = _uiState.value.budgetCategories
                
                if (unallocated > 0 && categories.isNotEmpty()) {
                    val amountPerCategory = unallocated / categories.size
                    
                    categories.forEach { category ->
                        val updatedCategory = category.copy(
                            allocatedAmount = category.allocatedAmount + amountPerCategory
                        )
                        budgetCategoryRepository.updateBudgetCategory(updatedCategory)
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to auto-balance budget: ${e.message}"
                )
            }
        }
    }

    fun deleteBudgetCategory(category: BudgetCategory) {
        viewModelScope.launch {
            try {
                budgetCategoryRepository.deleteBudgetCategory(category)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete category: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    private fun getCurrentPeriodValues(now: LocalDateTime, period: String): Triple<Int, Int?, Int?> {
        val date = now.date
        return when (period) {
            "weekly" -> {
                val weekOfYear = date.dayOfYear / 7 + 1
                Triple(date.year, null, weekOfYear)
            }
            "monthly" -> {
                Triple(date.year, date.monthNumber, null)
            }
            else -> Triple(date.year, null, null)
        }
    }
}

data class ZeroBudgetUiState(
    val budgetCategories: List<BudgetCategory> = emptyList(),
    val totalIncome: Double = 0.0,
    val totalAllocated: Double = 0.0,
    val totalSpent: Double = 0.0,
    val unallocatedAmount: Double = 0.0,
    val budgetPeriod: String = "weekly",
    val isBalanced: Boolean = false,
    val isLoading: Boolean = true,
    val error: String? = null
)
