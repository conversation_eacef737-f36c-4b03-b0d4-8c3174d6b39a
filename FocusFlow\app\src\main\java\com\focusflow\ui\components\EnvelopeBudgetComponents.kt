package com.focusflow.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.focusflow.data.model.BudgetCategory
import kotlin.math.abs

@Composable
fun EnvelopeGrid(
    categories: List<BudgetCategory>,
    onCategoryClick: (BudgetCategory) -> Unit,
    onTransferClick: (BudgetCategory) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = modifier.fillMaxWidth(),
        contentPadding = PaddingValues(8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(categories) { category ->
            EnvelopeBudgetCard(
                category = category,
                onClick = { onCategoryClick(category) },
                onTransferClick = { onTransferClick(category) }
            )
        }
    }
}

@Composable
fun EnvelopeBudgetCard(
    category: BudgetCategory,
    onClick: () -> Unit,
    onTransferClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val remaining = category.allocatedAmount - category.spentAmount
    val spentPercentage = if (category.allocatedAmount > 0) {
        (category.spentAmount / category.allocatedAmount).coerceIn(0.0, 1.0)
    } else 0.0
    
    val statusColor = when {
        spentPercentage > 1.0 -> Color(0xFFF44336) // Red - overspent
        spentPercentage > 0.8 -> Color(0xFFFF9800) // Orange - warning
        else -> Color(0xFF4CAF50) // Green - on track
    }
    
    val categoryColor = try {
        Color(android.graphics.Color.parseColor(category.categoryColor))
    } catch (e: Exception) {
        MaterialTheme.colors.primary
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(160.dp)
            .clickable { onClick() },
        elevation = 4.dp,
        shape = RoundedCornerShape(16.dp),
        backgroundColor = categoryColor.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Header with category name and transfer button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = category.name,
                    style = MaterialTheme.typography.subtitle1,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                IconButton(
                    onClick = onTransferClick,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.Default.SwapHoriz,
                        contentDescription = "Transfer",
                        modifier = Modifier.size(16.dp),
                        tint = categoryColor
                    )
                }
            }
            
            // Amount display
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "$${String.format("%.0f", remaining)}",
                    style = MaterialTheme.typography.h5,
                    fontWeight = FontWeight.Bold,
                    color = if (remaining >= 0) statusColor else Color(0xFFF44336)
                )
                Text(
                    text = "remaining",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
            
            // Progress indicator
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "$${String.format("%.0f", category.spentAmount)}",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "$${String.format("%.0f", category.allocatedAmount)}",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                LinearProgressIndicator(
                    progress = spentPercentage.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(6.dp)
                        .clip(RoundedCornerShape(3.dp)),
                    color = statusColor,
                    backgroundColor = MaterialTheme.colors.onSurface.copy(alpha = 0.1f)
                )
            }
            
            // Status indicator
            if (spentPercentage > 1.0) {
                Text(
                    text = "Over by $${String.format("%.0f", -remaining)}",
                    style = MaterialTheme.typography.caption,
                    color = Color(0xFFF44336),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun ZeroBudgetOverviewCard(
    totalIncome: Double,
    totalAllocated: Double,
    totalSpent: Double,
    unallocatedAmount: Double,
    isBalanced: Boolean,
    period: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 6.dp,
        shape = RoundedCornerShape(16.dp),
        backgroundColor = if (isBalanced) {
            Color(0xFF4CAF50).copy(alpha = 0.1f)
        } else {
            Color(0xFFFF9800).copy(alpha = 0.1f)
        }
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Zero-Based Budget",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
                
                Icon(
                    if (isBalanced) Icons.Default.CheckCircle else Icons.Default.Warning,
                    contentDescription = null,
                    tint = if (isBalanced) Color(0xFF4CAF50) else Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Budget amounts in a grid
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                BudgetAmountColumn(
                    label = "Income",
                    amount = totalIncome,
                    color = Color(0xFF2196F3)
                )
                BudgetAmountColumn(
                    label = "Allocated",
                    amount = totalAllocated,
                    color = Color(0xFF9C27B0)
                )
                BudgetAmountColumn(
                    label = "Spent",
                    amount = totalSpent,
                    color = Color(0xFFFF9800)
                )
                BudgetAmountColumn(
                    label = "Unallocated",
                    amount = unallocatedAmount,
                    color = if (unallocatedAmount == 0.0) Color(0xFF4CAF50) else Color(0xFFF44336)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Status message
            Text(
                text = when {
                    isBalanced -> "✅ Budget is perfectly balanced!"
                    unallocatedAmount > 0 -> "💰 You have $${String.format("%.0f", unallocatedAmount)} to allocate"
                    else -> "⚠️ You've over-allocated by $${String.format("%.0f", abs(unallocatedAmount))}"
                },
                style = MaterialTheme.typography.body2,
                color = when {
                    isBalanced -> Color(0xFF4CAF50)
                    unallocatedAmount > 0 -> Color(0xFF2196F3)
                    else -> Color(0xFFF44336)
                },
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            Text(
                text = "This ${period.replaceFirstChar { it.lowercase() }}",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun BudgetAmountColumn(
    label: String,
    amount: Double,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = "$${String.format("%.0f", amount)}",
            style = MaterialTheme.typography.subtitle1,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@Composable
fun IncomeSetupCard(
    currentIncome: Double,
    period: String,
    onIncomeSet: (Double) -> Unit,
    modifier: Modifier = Modifier
) {
    var incomeText by remember { mutableStateOf(if (currentIncome > 0) currentIncome.toString() else "") }
    var showDialog by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(0xFF2196F3).copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.AccountBalance,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = Color(0xFF2196F3)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = if (currentIncome > 0) {
                    "$${String.format("%.0f", currentIncome)} ${period.replaceFirstChar { it.lowercase() }}"
                } else {
                    "Set Your ${period.replaceFirstChar { it.titlecase() }} Income"
                },
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = if (currentIncome > 0) {
                    "Tap to update your income amount"
                } else {
                    "Enter your total income to start zero-based budgeting"
                },
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = { showDialog = true },
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = Color(0xFF2196F3)
                )
            ) {
                Text(if (currentIncome > 0) "Update Income" else "Set Income")
            }
        }
    }

    if (showDialog) {
        IncomeInputDialog(
            currentAmount = incomeText,
            period = period,
            onAmountChanged = { incomeText = it },
            onConfirm = {
                incomeText.toDoubleOrNull()?.let { amount ->
                    if (amount > 0) {
                        onIncomeSet(amount)
                        showDialog = false
                    }
                }
            },
            onDismiss = { showDialog = false }
        )
    }
}

@Composable
fun IncomeInputDialog(
    currentAmount: String,
    period: String,
    onAmountChanged: (String) -> Unit,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Set ${period.replaceFirstChar { it.titlecase() }} Income",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Enter your total ${period.lowercase()} income from all sources:",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(12.dp))

                OutlinedTextField(
                    value = currentAmount,
                    onValueChange = onAmountChanged,
                    label = { Text("${period.replaceFirstChar { it.titlecase() }} Income") },
                    placeholder = { Text("0.00") },
                    leadingIcon = { Text("$") },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Decimal
                    ),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                enabled = currentAmount.toDoubleOrNull() != null &&
                         (currentAmount.toDoubleOrNull() ?: 0.0) > 0
            ) {
                Text("Set Income")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun TransferFundsDialog(
    fromCategory: BudgetCategory,
    availableCategories: List<BudgetCategory>,
    onTransfer: (Long, Double) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedCategoryId by remember { mutableStateOf<Long?>(null) }
    var transferAmount by remember { mutableStateOf("") }
    var expanded by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Transfer Funds",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Transfer from: ${fromCategory.name}",
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "Available: $${String.format("%.2f", fromCategory.allocatedAmount - fromCategory.spentAmount)}",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Category selector
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = availableCategories.find { it.id == selectedCategoryId }?.name ?: "",
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Transfer to") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier.fillMaxWidth()
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        availableCategories.filter { it.id != fromCategory.id }.forEach { category ->
                            DropdownMenuItem(
                                onClick = {
                                    selectedCategoryId = category.id
                                    expanded = false
                                }
                            ) {
                                Text(category.name)
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                OutlinedTextField(
                    value = transferAmount,
                    onValueChange = { transferAmount = it },
                    label = { Text("Amount") },
                    placeholder = { Text("0.00") },
                    leadingIcon = { Text("$") },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Decimal
                    ),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    selectedCategoryId?.let { categoryId ->
                        transferAmount.toDoubleOrNull()?.let { amount ->
                            if (amount > 0 && amount <= (fromCategory.allocatedAmount - fromCategory.spentAmount)) {
                                onTransfer(categoryId, amount)
                            }
                        }
                    }
                },
                enabled = selectedCategoryId != null &&
                         transferAmount.toDoubleOrNull() != null &&
                         (transferAmount.toDoubleOrNull() ?: 0.0) > 0 &&
                         (transferAmount.toDoubleOrNull() ?: 0.0) <= (fromCategory.allocatedAmount - fromCategory.spentAmount)
            ) {
                Text("Transfer")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun EnhancedBudgetCategoryCard(
    category: BudgetCategory,
    onEdit: () -> Unit,
    onDelete: () -> Unit,
    onTransfer: () -> Unit,
    modifier: Modifier = Modifier
) {
    val remaining = category.allocatedAmount - category.spentAmount
    val spentPercentage = if (category.allocatedAmount > 0) {
        (category.spentAmount / category.allocatedAmount).coerceIn(0.0, 1.0)
    } else 0.0

    val statusColor = when {
        spentPercentage > 1.0 -> Color(0xFFF44336) // Red - overspent
        spentPercentage > 0.8 -> Color(0xFFFF9800) // Orange - warning
        else -> Color(0xFF4CAF50) // Green - on track
    }

    val categoryColor = try {
        Color(android.graphics.Color.parseColor(category.categoryColor))
    } catch (e: Exception) {
        MaterialTheme.colors.primary
    }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = categoryColor.copy(alpha = 0.05f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header with category name and actions
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(categoryColor, shape = androidx.compose.foundation.shape.CircleShape)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = category.name,
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Medium
                    )
                }

                Row {
                    IconButton(
                        onClick = onTransfer,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.SwapHoriz,
                            contentDescription = "Transfer",
                            modifier = Modifier.size(18.dp),
                            tint = categoryColor
                        )
                    }
                    IconButton(
                        onClick = onEdit,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit",
                            modifier = Modifier.size(18.dp)
                        )
                    }
                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = MaterialTheme.colors.error,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Amount information
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Remaining",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "$${String.format("%.2f", remaining)}",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold,
                        color = if (remaining >= 0) statusColor else Color(0xFFF44336)
                    )
                }

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "Allocated",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "$${String.format("%.2f", category.allocatedAmount)}",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold,
                        color = categoryColor
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Progress bar with labels
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "$${String.format("%.2f", category.spentAmount)} spent",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "${String.format("%.1f", spentPercentage * 100)}%",
                        style = MaterialTheme.typography.body2,
                        color = statusColor,
                        fontWeight = FontWeight.Medium
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                LinearProgressIndicator(
                    progress = spentPercentage.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = statusColor,
                    backgroundColor = MaterialTheme.colors.onSurface.copy(alpha = 0.1f)
                )
            }

            // Status message for overspending
            if (spentPercentage > 1.0) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = Color(0xFFF44336)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Over budget by $${String.format("%.2f", -remaining)}",
                        style = MaterialTheme.typography.caption,
                        color = Color(0xFFF44336),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}
