package com.focusflow.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.BudgetCategory
import com.focusflow.ui.components.*
import com.focusflow.ui.viewmodel.ZeroBudgetViewModel
import com.focusflow.ui.viewmodel.DefaultBudgetCategories

@Composable
fun EnhancedBudgetScreen(
    viewModel: ZeroBudgetViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    var showAddCategoryDialog by remember { mutableStateOf(false) }
    var showQuickSetupDialog by remember { mutableStateOf(false) }
    var showTransferDialog by remember { mutableStateOf(false) }
    var selectedTransferCategory by remember { mutableStateOf<BudgetCategory?>(null) }
    var viewMode by remember { mutableStateOf(ViewMode.ENVELOPE) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with view mode toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Zero-Based Budget",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(
                    onClick = { viewMode = if (viewMode == ViewMode.ENVELOPE) ViewMode.LIST else ViewMode.ENVELOPE }
                ) {
                    Icon(
                        if (viewMode == ViewMode.ENVELOPE) Icons.Default.List else Icons.Default.Apps,
                        contentDescription = "Toggle view"
                    )
                }
                
                IconButton(onClick = { showAddCategoryDialog = true }) {
                    Icon(Icons.Default.Add, contentDescription = "Add Category")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Income setup card
                item {
                    IncomeSetupCard(
                        currentIncome = uiState.totalIncome,
                        period = uiState.budgetPeriod,
                        onIncomeSet = { amount -> viewModel.setIncome(amount) }
                    )
                }

                // Budget overview
                if (uiState.totalIncome > 0) {
                    item {
                        ZeroBudgetOverviewCard(
                            totalIncome = uiState.totalIncome,
                            totalAllocated = uiState.totalAllocated,
                            totalSpent = uiState.totalSpent,
                            unallocatedAmount = uiState.unallocatedAmount,
                            isBalanced = uiState.isBalanced,
                            period = uiState.budgetPeriod
                        )
                    }
                }

                // Quick actions
                if (uiState.totalIncome > 0 && !uiState.isBalanced) {
                    item {
                        QuickActionsCard(
                            unallocatedAmount = uiState.unallocatedAmount,
                            onAutoBalance = { viewModel.autoBalanceBudget() },
                            onQuickSetup = { showQuickSetupDialog = true }
                        )
                    }
                }

                // Budget categories
                if (uiState.budgetCategories.isEmpty() && uiState.totalIncome > 0) {
                    item {
                        EmptyBudgetCategoriesCard(
                            onQuickSetup = { showQuickSetupDialog = true },
                            onAddCategory = { showAddCategoryDialog = true }
                        )
                    }
                } else if (uiState.budgetCategories.isNotEmpty()) {
                    item {
                        Text(
                            text = "Budget Categories",
                            style = MaterialTheme.typography.h6,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    if (viewMode == ViewMode.ENVELOPE) {
                        item {
                            EnvelopeGrid(
                                categories = uiState.budgetCategories,
                                onCategoryClick = { /* TODO: Navigate to category details */ },
                                onTransferClick = { category ->
                                    selectedTransferCategory = category
                                    showTransferDialog = true
                                }
                            )
                        }
                    } else {
                        items(uiState.budgetCategories) { category ->
                            EnhancedBudgetCategoryCard(
                                category = category,
                                onEdit = { /* TODO: Edit category */ },
                                onDelete = { viewModel.deleteBudgetCategory(category) },
                                onTransfer = { 
                                    selectedTransferCategory = category
                                    showTransferDialog = true
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    // Dialogs
    if (showAddCategoryDialog) {
        AddBudgetCategoryDialog(
            onDismiss = { showAddCategoryDialog = false },
            onAddCategory = { name, amount ->
                viewModel.addBudgetCategory(name, amount)
                showAddCategoryDialog = false
            }
        )
    }

    if (showQuickSetupDialog) {
        QuickSetupDialog(
            unallocatedAmount = uiState.unallocatedAmount,
            onDismiss = { showQuickSetupDialog = false },
            onSetupComplete = { categories ->
                categories.forEach { (name, amount) ->
                    viewModel.addBudgetCategory(name, amount)
                }
                showQuickSetupDialog = false
            }
        )
    }

    selectedTransferCategory?.let { category ->
        if (showTransferDialog) {
            TransferFundsDialog(
                fromCategory = category,
                availableCategories = uiState.budgetCategories,
                onTransfer = { toCategoryId, amount ->
                    viewModel.transferBetweenEnvelopes(category.id, toCategoryId, amount)
                    showTransferDialog = false
                    selectedTransferCategory = null
                },
                onDismiss = {
                    showTransferDialog = false
                    selectedTransferCategory = null
                }
            )
        }
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }
}

enum class ViewMode {
    ENVELOPE, LIST
}

@Composable
fun QuickActionsCard(
    unallocatedAmount: Double,
    onAutoBalance: () -> Unit,
    onQuickSetup: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        backgroundColor = Color(0xFFF3E5F5)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick Actions",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (unallocatedAmount > 0) {
                Text(
                    text = "You have $${String.format("%.0f", unallocatedAmount)} unallocated",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onAutoBalance,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Auto Balance")
                    }
                    
                    Button(
                        onClick = onQuickSetup,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Quick Setup")
                    }
                }
            }
        }
    }
}

@Composable
fun EmptyBudgetCategoriesCard(
    onQuickSetup: () -> Unit,
    onAddCategory: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.AccountBalance,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Create Budget Categories",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Allocate your income into spending categories to track where every dollar goes",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(onClick = onAddCategory) {
                    Text("Add Category")
                }

                Button(onClick = onQuickSetup) {
                    Text("Quick Setup")
                }
            }
        }
    }
}

@Composable
fun AddBudgetCategoryDialog(
    onDismiss: () -> Unit,
    onAddCategory: (String, Double) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var amount by remember { mutableStateOf("") }
    var selectedColor by remember { mutableStateOf("#2196F3") }

    val predefinedColors = listOf(
        "#2196F3", "#4CAF50", "#FF9800", "#9C27B0",
        "#F44336", "#00BCD4", "#795548", "#607D8B"
    )

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Add Budget Category",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Category Name") },
                    placeholder = { Text("e.g., Groceries, Entertainment") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Spacer(modifier = Modifier.height(12.dp))

                OutlinedTextField(
                    value = amount,
                    onValueChange = { amount = it },
                    label = { Text("Budget Amount") },
                    placeholder = { Text("0.00") },
                    leadingIcon = { Text("$") },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Decimal
                    ),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Color picker
                Text(
                    text = "Category Color",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(4.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    predefinedColors.take(4).forEach { color ->
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    Color(android.graphics.Color.parseColor(color)),
                                    shape = androidx.compose.foundation.shape.CircleShape
                                )
                                .clickable { selectedColor = color }
                                .then(
                                    if (selectedColor == color) {
                                        Modifier.padding(2.dp)
                                    } else Modifier
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            if (selectedColor == color) {
                                Icon(
                                    Icons.Default.Check,
                                    contentDescription = null,
                                    tint = Color.White,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    predefinedColors.drop(4).forEach { color ->
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    Color(android.graphics.Color.parseColor(color)),
                                    shape = androidx.compose.foundation.shape.CircleShape
                                )
                                .clickable { selectedColor = color }
                                .then(
                                    if (selectedColor == color) {
                                        Modifier.padding(2.dp)
                                    } else Modifier
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            if (selectedColor == color) {
                                Icon(
                                    Icons.Default.Check,
                                    contentDescription = null,
                                    tint = Color.White,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val amountDouble = amount.toDoubleOrNull()
                    if (name.isNotBlank() && amountDouble != null && amountDouble > 0) {
                        onAddCategory(name, amountDouble)
                    }
                },
                enabled = name.isNotBlank() &&
                         amount.toDoubleOrNull() != null &&
                         (amount.toDoubleOrNull() ?: 0.0) > 0
            ) {
                Text("Add Category")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun QuickSetupDialog(
    unallocatedAmount: Double,
    onDismiss: () -> Unit,
    onSetupComplete: (List<Pair<String, Double>>) -> Unit
) {
    var selectedCategories by remember {
        mutableStateOf(
            DefaultBudgetCategories.categories.mapValues {
                kotlin.math.min(it.value, unallocatedAmount / DefaultBudgetCategories.categories.size)
            }.toMutableMap()
        )
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Quick Budget Setup",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Allocate your $${String.format("%.0f", unallocatedAmount)} across these categories:",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(12.dp))

                selectedCategories.forEach { (category, amount) ->
                    var categoryAmount by remember { mutableStateOf(amount.toString()) }

                    OutlinedTextField(
                        value = categoryAmount,
                        onValueChange = {
                            categoryAmount = it
                            it.toDoubleOrNull()?.let { newAmount ->
                                selectedCategories[category] = newAmount
                            }
                        },
                        label = { Text(category) },
                        leadingIcon = { Text("$") },
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Decimal
                        ),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                val totalAllocated = selectedCategories.values.sum()
                val remaining = unallocatedAmount - totalAllocated

                Text(
                    text = "Remaining: $${String.format("%.2f", remaining)}",
                    style = MaterialTheme.typography.caption,
                    color = if (remaining >= 0) Color(0xFF4CAF50) else Color(0xFFF44336),
                    fontWeight = FontWeight.Medium
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onSetupComplete(selectedCategories.filter { it.value > 0 }.toList())
                }
            ) {
                Text("Create Categories")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
